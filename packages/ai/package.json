{"name": "@repo/ai", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@ai-sdk/openai": "^1.3.15", "@t3-oss/env-nextjs": "^0.12.0", "ai": "^4.3.9", "react": "^19.1.0", "react-markdown": "^10.1.0", "tailwind-merge": "^3.2.0", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "^19.1.2"}}