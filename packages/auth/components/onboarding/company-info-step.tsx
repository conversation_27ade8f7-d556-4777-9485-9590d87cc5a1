'use client';

import {
  Check,
  ChevronsUpDown,
  LoaderCircle,
} from '@repo/design-system/components/icons';
import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@repo/design-system/components/ui/command';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import { ScrollArea } from '@repo/design-system/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { cn } from '@repo/design-system/lib/utils';
import { useEffect, useState } from 'react';
import { msicOptions } from '../../constants/msic';
import type { CompanyInfoData } from '../../schemas/business-form-schema';
import { companyInfoSchema } from '../../schemas/business-form-schema';
import { useOnboardingStore } from '../../store/onboarding-store';
import { BusinessTypeSelector } from './business-type-selector';

interface CompanyInfoStepProps {
  onNext: () => void;
  onBack: () => void;
}

// Registration type mapping for labels and descriptions
const REGISTRATION_TYPE_INFO = {
  BRN: {
    label: 'Business Registration Number',
    placeholder: 'Enter business registration number',
    description: 'The official registration number of your business entity',
  },
  NRIC: {
    label: 'NRIC Number',
    placeholder: 'Enter your NRIC number',
    description: 'Your National Registration Identity Card number',
  },
  PASSPORT: {
    label: 'Passport Number',
    placeholder: 'Enter your passport number',
    description: 'Your international passport identification number',
  },
  ARMY: {
    label: 'Army ID Number',
    placeholder: 'Enter your army ID number',
    description: 'Your military identification number',
  },
};

export function CompanyInfoStep({ onNext, onBack }: CompanyInfoStepProps) {
  const { formData, updateFormData } = useOnboardingStore();
  const [open, setOpen] = useState(false);

  const form = useForm<CompanyInfoData>({
    resolver: zodResolver(companyInfoSchema),
    defaultValues: {
      name: formData.name || '',
      tin_code: formData.tin_code || '',
      registration_number: formData.registration_number || '',
      registration_type: formData.registration_type || 'BRN',
      business_activity_description:
        formData.business_activity_description || '',
      msic_code: formData.msic_code || '',
      business_type: formData.business_type || undefined,
    },
  });

  const { isSubmitting } = form.formState;
  const registrationType = form.watch('registration_type');

  // Update form when store data changes
  useEffect(() => {
    form.reset({
      name: formData.name || '',
      tin_code: formData.tin_code || '',
      registration_number: formData.registration_number || '',
      registration_type: formData.registration_type || 'BRN',
      business_activity_description:
        formData.business_activity_description || '',
      msic_code: formData.msic_code || '',
      business_type: formData.business_type || undefined,
    });
  }, [form, formData]);

  function onSubmit(values: CompanyInfoData) {
    updateFormData(values);
    onNext();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Business Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter your business name"
                    disabled={isSubmitting}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tin_code"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>TIN Code</FormLabel>

                <div className="flex space-x-2">
                  <FormControl>
                    <Input
                      placeholder="Enter your TIN code"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <span className='text-muted-foreground text-xs'>
                    TODO: TIN verification with lhdn
                  </span>
                  <Button
                    id="verify-tax-id"
                    type="button"
                    // onClick={verifyTaxId}
                    // disabled={taxIdVerified}
                  >
                    Verify
                    {/* {taxIdVerified ? 'Verified' : 'Verify'} */}
                  </Button>
                </div>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="registration_type"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Registration Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl className="w-full">
                      <SelectTrigger>
                        <SelectValue placeholder="Select registration type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="BRN">BRN</SelectItem>
                      <SelectItem value="NRIC">NRIC</SelectItem>
                      <SelectItem value="PASSPORT">Passport</SelectItem>
                      <SelectItem value="ARMY">Army</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="registration_number"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>
                    {REGISTRATION_TYPE_INFO[registrationType]?.label ||
                      'Registration Number'}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={
                        REGISTRATION_TYPE_INFO[registrationType]?.placeholder ||
                        'Enter registration number'
                      }
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  {REGISTRATION_TYPE_INFO[registrationType]?.description && (
                    <FormDescription>
                      {REGISTRATION_TYPE_INFO[registrationType].description}
                    </FormDescription>
                  )}
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="business_activity_description"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Business Activity Description</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter business activity description"
                    disabled={isSubmitting}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <BusinessTypeSelector form={form} isSubmitting={isSubmitting} />

          <FormField
            control={form.control}
            name="msic_code"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>MSIC Code</FormLabel>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        // biome-ignore lint/a11y/useSemanticElements: <explanation>
                        role="combobox"
                        aria-expanded={open}
                        className="w-full justify-between overflow-hidden"
                      >
                        <span className="truncate">
                          {field.value
                            ? msicOptions.find(
                                (option) => option.Code === field.value
                              )?.Description
                            : 'Select MSIC Code...'}
                        </span>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-[400px] p-0">
                    <Command>
                      <CommandInput placeholder="Search MSIC code or description..." />
                      <CommandEmpty>No MSIC code found.</CommandEmpty>
                      <CommandGroup className="max-h-[300px] overflow-y-auto">
                        <ScrollArea className="h-72">
                          {msicOptions.map((option) => (
                            <CommandItem
                              key={option.Code}
                              value={`${option.Code} ${option.Description}`}
                              onSelect={() => {
                                field.onChange(option.Code);
                                setOpen(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  field.value === option.Code
                                    ? 'opacity-100'
                                    : 'opacity-0'
                                )}
                              />
                              {option.Code} - {option.Description}
                            </CommandItem>
                          ))}
                        </ScrollArea>
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Next
          </Button>
        </div>
      </form>
    </Form>
  );
}
