{"name": "@repo/webhooks", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@repo/auth": "workspace:*", "@t3-oss/env-nextjs": "^0.12.0", "server-only": "^0.0.1", "svix": "^1.64.1", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.3"}}