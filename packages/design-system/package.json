{"name": "@repo/design-system", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/observability": "workspace:*", "chrono-node": "^2.8.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "geist": "^1.3.1", "input-otp": "^1.4.2", "lucide-react": "^0.488.0", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "radash": "^12.1.0", "react": "19.1.0", "react-day-picker": "^9.7.0", "react-hook-form": "^7.55.0", "react-moveable": "^0.56.0", "react-phone-number-input": "^3.4.12", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "server-only": "^0.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "timescape": "^0.7.1", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.8.3"}}