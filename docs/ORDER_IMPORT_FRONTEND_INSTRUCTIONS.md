# Order Import Frontend Integration Instructions

This document provides detailed instructions for implementing CSV/Excel/PDF order import functionality in the frontend application. The backend already has the necessary endpoints and services for handling file uploads and processing imports.

## Overview

The order import functionality will allow users to:

1. Download template files (CSV/Excel) for bulk order imports
2. Upload CSV/Excel files containing order data
3. Upload PDF invoices for OCR-based order creation (future enhancement)
4. View import results and handle errors
5. Manage imported orders

## Backend API Endpoints

The following backend API endpoints are already implemented:

- `GET /app/v1/orders/template/:format?` - Download a template file (CSV or Excel)
- `POST /app/v1/orders/bulk-import` - Upload and process a CSV or Excel file

## Frontend Implementation Requirements

### 1. Import Component Structure

Create the following components:

#### 1.1 OrderImportPage

Main page that contains the import interface with tabs for different import methods.

```tsx
// src/pages/OrderImport.tsx
import React from 'react';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import CSVExcelImport from '../components/imports/CSVExcelImport';
import PDFImport from '../components/imports/PDFImport';

const OrderImportPage = () => {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Import Orders</h1>

      <Tabs defaultValue="csv-excel" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="csv-excel">CSV/Excel Import</TabsTrigger>
          <TabsTrigger value="pdf">PDF Import</TabsTrigger>
        </TabsList>

        <TabsContent value="csv-excel">
          <CSVExcelImport />
        </TabsContent>

        <TabsContent value="pdf">
          <PDFImport />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OrderImportPage;
```

#### 1.2 CSVExcelImport Component

Component for handling CSV and Excel file imports.

```tsx
// src/components/imports/CSVExcelImport.tsx
import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { api } from '../../services/api';
import ImportResultsTable from './ImportResultsTable';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Download, Upload, Loader2 } from "lucide-react";

const CSVExcelImport = () => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setFile(acceptedFiles[0]);
      setError(null);
      setUploadResult(null);
    },
  });

  const downloadTemplate = async (format: 'csv' | 'xlsx') => {
    try {
      const response = await api.get(`/orders/template/${format}`, {
        responseType: 'blob',
      });

      // Create a download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `order_import_template.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError('Failed to download template. Please try again.');
      console.error(err);
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    setError(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await api.post('/orders/bulk-import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setUploadResult(response.data.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to upload file. Please try again.');
      console.error(err);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">CSV/Excel Import</h2>

      <Card>
        <CardHeader>
          <CardTitle>Import Orders</CardTitle>
          <CardDescription>
            Download a template file, fill it with your order data, and upload it to import orders in bulk.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">1. Download Template</h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => downloadTemplate('csv')}
              >
                <Download className="mr-2 h-4 w-4" />
                CSV Template
              </Button>
              <Button
                variant="outline"
                onClick={() => downloadTemplate('xlsx')}
              >
                <Download className="mr-2 h-4 w-4" />
                Excel Template
              </Button>
            </div>
          </div>

          <Separator />

          <div className="space-y-2">
            <h3 className="text-sm font-medium">2. Upload File</h3>

            <div
              {...getRootProps()}
              className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center cursor-pointer hover:border-primary"
            >
              <input {...getInputProps()} />
              <Upload className="mx-auto h-10 w-10 text-gray-400 mb-2" />
              <p className="text-sm text-gray-600">
                Drag & drop a CSV or Excel file here, or click to select a file
              </p>
              {file && (
                <p className="mt-2 text-sm font-medium">
                  Selected file: {file.name}
                </p>
              )}
            </div>

            <div className="flex justify-center mt-4">
              <Button
                disabled={!file || isUploading}
                onClick={handleUpload}
              >
                {isUploading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isUploading ? 'Uploading...' : 'Upload and Import'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {uploadResult && (
        <ImportResultsTable result={uploadResult} />
      )}
    </div>
  );
};

export default CSVExcelImport;
```

#### 1.3 PDFImport Component

Component for handling PDF file imports (for future OCR implementation).

```tsx
// src/components/imports/PDFImport.tsx
import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload } from "lucide-react";

const PDFImport = () => {
  const [file, setFile] = useState<File | null>(null);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setFile(acceptedFiles[0]);
    },
  });

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">PDF Import (Coming Soon)</h2>

      <Card>
        <CardHeader>
          <CardTitle>PDF Import</CardTitle>
          <CardDescription>
            Upload PDF invoices to automatically extract order data using OCR technology.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <Alert>
            <AlertDescription>
              This feature is coming soon. PDF import with OCR will allow you to extract order data from invoice PDFs.
            </AlertDescription>
          </Alert>

          <div
            {...getRootProps()}
            className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center cursor-pointer opacity-60"
          >
            <input {...getInputProps()} disabled />
            <Upload className="mx-auto h-10 w-10 text-gray-400 mb-2" />
            <p className="text-sm text-gray-600">
              PDF import functionality coming soon
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PDFImport;
```

#### 1.4 ImportResultsTable Component

Component to display the results of an import operation.

```tsx
// src/components/imports/ImportResultsTable.tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";

interface ImportResultsTableProps {
  result: {
    totalRecords: number;
    successCount: number;
    failureCount: number;
    errors: Array<{
      row: number;
      message: string;
      data?: Record<string, any>;
    }>;
    successfulOrderIds: number[];
  };
}

const ImportResultsTable: React.FC<ImportResultsTableProps> = ({ result }) => {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Import Results</h2>

      <Card>
        <CardHeader>
          <CardTitle>Results Summary</CardTitle>
        </CardHeader>

        <CardContent>
          <div className="flex gap-6 mb-6">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Records</p>
              <p className="text-2xl font-bold">{result.totalRecords}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Successfully Imported</p>
              <p className="text-2xl font-bold text-green-600">{result.successCount}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Failed</p>
              <p className="text-2xl font-bold text-red-600">{result.failureCount}</p>
            </div>
          </div>

          {result.failureCount > 0 && (
            <>
              <h3 className="text-sm font-medium mb-2">Error Details</h3>
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Row</TableHead>
                      <TableHead>Invoice Code</TableHead>
                      <TableHead>Error Message</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {result.errors.map((error, index) => (
                      <TableRow key={index}>
                        <TableCell>{error.row}</TableCell>
                        <TableCell>{error.data?.invoiceCode || 'N/A'}</TableCell>
                        <TableCell>{error.message}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </>
          )}

          {result.successCount > 0 && (
            <Alert className="mt-4">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Successfully imported {result.successCount} orders. You can view them in the Orders section.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ImportResultsTable;
```

### 2. API Service

Create or update the API service to include methods for the import functionality:

```tsx
// src/services/api.ts
import axios from 'axios';

const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3333/app/v1';

export const api = axios.create({
  baseURL,
});

// Add auth interceptor
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Add company ID to headers if available
  const companyId = localStorage.getItem('currentCompanyId');
  if (companyId) {
    config.headers['company_id'] = companyId;
  }

  return config;
});

// Order import specific methods
export const orderImportApi = {
  downloadTemplate: async (format: 'csv' | 'xlsx') => {
    return api.get(`/orders/template/${format}`, {
      responseType: 'blob',
    });
  },

  uploadFile: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    return api.post('/orders/bulk-import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};
```

### 3. Navigation Integration

Add the import functionality to the main navigation:

```tsx
// Example navigation component using Shadcn UI
import { Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

// In your navigation component
<Button variant="ghost" asChild className="w-full justify-start">
  <Link to="/orders/import" className="flex items-center">
    <Upload className="mr-2 h-4 w-4" />
    <span>Import Orders</span>
  </Link>
</Button>
```

### 4. Route Configuration

Add the route for the import page:

```tsx
// src/routes/index.tsx
import OrderImportPage from '../pages/OrderImport';

// Inside your Routes component
<Route path="/orders/import" element={<OrderImportPage />} />
```

## Implementation Guidelines

### File Format Support

- CSV files (.csv)
- Excel files (.xlsx, .xls)
- PDF files (future implementation)

### Error Handling

- Validate file type and size before upload
- Display clear error messages for invalid files
- Show detailed error information for failed imports
- Provide guidance on how to fix common import issues

### User Experience

- Implement drag-and-drop functionality for file uploads
- Show loading indicators during file processing
- Provide clear success/error feedback
- Allow users to download template files easily
- Display a summary of import results

### Security Considerations

- Validate file content on the server
- Implement proper authentication and authorization
- Set appropriate file size limits
- Sanitize all user inputs

## Future Enhancements

1. **PDF Import with OCR**: Implement OCR functionality to extract order data from PDF invoices
2. **Import History**: Add a section to view past import operations and their results
3. **Batch Processing**: Allow users to queue multiple files for batch processing
4. **Custom Templates**: Let users create and save custom import templates
5. **Data Validation**: Add client-side validation of CSV/Excel data before upload
6. **Preview Mode**: Show a preview of the data before finalizing the import

## Testing Checklist

- [ ] Test downloading template files in different formats
- [ ] Test uploading valid CSV files
- [ ] Test uploading valid Excel files
- [ ] Test uploading files with invalid format
- [ ] Test uploading files that exceed size limit
- [ ] Test uploading files with missing required fields
- [ ] Test uploading files with invalid data
- [ ] Test error handling and display
- [ ] Test import results display
- [ ] Test navigation to imported orders
