# Organization Context Setup Guide

## Overview
This guide explains how the organization context has been set up to work with subdomain routing and better-auth organization plugin.

## Architecture

### 1. Subdomain Resolution Flow
```
subdomain.domain.com → middleware → layout → database query → organization context
```

1. **Middleware** (`apps/portal/middleware.ts`) extracts subdomain and rewrites URL
2. **Layout** (`apps/portal/app/[locale]/[subdomain]/layout.tsx`) queries database for organization
3. **Organization Context** receives initial organization and manages state

### 2. Better-Auth Integration
- Uses `organization.setActive()` to set active organization in better-auth
- Automatically syncs with user session when authenticated
- Provides organization management functions

## Key Components

### OrganizationProvider
```tsx
<OrganizationProvider initialOrganization={organization}>
  {children}
</OrganizationProvider>
```

### useOrganization Hook
```tsx
const { 
  organization,           // Current organization
  isOrganizationPortal,  // Boolean if in org portal
  isLoading,             // Loading state
  setActiveOrganization  // Function to change active org
} = useOrganization();
```

## Usage Examples

### 1. Display Organization Info
```tsx
function MyComponent() {
  const { organization } = useOrganization();
  
  if (!organization) return null;
  
  return (
    <div>
      <h1>{organization.name}</h1>
      <p>Slug: {organization.slug}</p>
    </div>
  );
}
```

### 2. Check if in Organization Portal
```tsx
function MyComponent() {
  const { isOrganizationPortal } = useOrganization();
  
  return (
    <div>
      {isOrganizationPortal ? (
        <p>You're in an organization portal</p>
      ) : (
        <p>You're on the main site</p>
      )}
    </div>
  );
}
```

### 3. Switch Organization (for authenticated users)
```tsx
function OrganizationSwitcher() {
  const { setActiveOrganization } = useOrganization();
  
  const handleSwitch = async (org) => {
    await setActiveOrganization(org);
  };
  
  return (
    <button onClick={() => handleSwitch(someOrg)}>
      Switch Organization
    </button>
  );
}
```

## Database Requirements

Ensure your organization table has:
- `id` (string, primary key)
- `name` (string, required)
- `slug` (string, unique, required) - used for subdomain
- `logo` (string, optional)

## Testing

### 1. Create Test Organization
```sql
INSERT INTO organization (id, name, slug, logo) 
VALUES ('org-1', 'Acme Corp', 'acme-corp', null);
```

### 2. Test URLs
- Main site: `http://localhost:3000`
- Organization portal: `http://acme-corp.localhost:3000`

### 3. Verify Context
Check that:
- Organization data loads correctly
- Better-auth active organization is set when authenticated
- Loading states work properly
- Error handling for non-existent organizations

## Next Steps

1. **Add organization-specific features**:
   - Custom branding based on organization
   - Organization-specific settings
   - Member management

2. **Enhance security**:
   - Check user permissions for organization access
   - Add organization-based route protection

3. **Add caching**:
   - Cache organization data in Redis
   - Implement organization data revalidation

4. **Extend functionality**:
   - Organization switching UI
   - Multi-organization user support
   - Organization invitation flows

## Troubleshooting

### Organization Not Found
- Check database for organization with matching slug
- Verify subdomain extraction in middleware
- Check database connection

### Better-Auth Integration Issues
- Ensure user is authenticated before setting active organization
- Check better-auth organization plugin configuration
- Verify organization permissions

### Loading States
- Check if `initialOrganization` is being passed correctly
- Verify useEffect dependencies in organization context
- Check for proper error handling
