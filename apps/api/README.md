# API Service

This service provides API endpoints for the application. It uses API key authentication from the better-auth package.

## API Key Authentication

All API endpoints in this service require API key authentication. API keys can be generated and managed through the application's developer settings.

### Implementation

API key authentication is implemented directly in the API routes using the `withApi<PERSON>ey<PERSON>uth` higher-order function in `app/lib/auth.ts`. This function:

1. Extracts the API key from the request headers
2. Verifies the API key using the better-auth package
3. Returns a 401 Unauthorized response if no valid API key is found
4. Adds organization information to the request headers if available
5. Allows the request to proceed if a valid API key is found

To use API key authentication in a route, wrap your handler function with `withA<PERSON><PERSON><PERSON><PERSON><PERSON>`:

```typescript
import { withApi<PERSON><PERSON>Auth } from '@/app/lib/auth';

const handler = async (request: NextRequest) => {
  // Your handler logic here
};

export const GET = withApiKeyAuth(handler);
```

### How to Use API Keys

To authenticate API requests, include the API key in the Authorization header:

```
Authorization: Bearer inv_your_api_key_here
```

or as an X-API-Key header:

```
X-API-Key: inv_your_api_key_here
```

### Environment Variables

Make sure the following environment variables are set:

- `BETTER_AUTH_SECRET`: Secret key for better-auth
- `AUTH_ISSUER`: Issuer for JWT tokens

## API Endpoints

The API provides endpoints for:

- User management
- Organization management
- File uploads
- Authentication

## Development

To run the API service locally:

```bash
pnpm dev
```

The API will be available at http://localhost:3002 by default.
