import { env } from '@/env';
import { auth } from '@repo/auth/server';
import { log } from '@repo/observability/log';
import { NextRequest, NextResponse } from 'next/server';
import { withCors } from './api';

/**
 * Type for route handler functions
 */
type RouteHandler<T = unknown> = (
  req: NextRequest,
  context: { params: Promise<T> }
) => Promise<NextResponse>;

/**
 * Extracts the API key from the request headers
 * Checks both Authorization header (Bearer token) and X-API-Key header
 */
export const extractApiKey = (
  request: NextRequest
): [string | null, string | null] => {
  // Check Authorization header first (Bearer token)
  const authHeader = request.headers.get('Authorization');
  // Check X-API-Key header
  const apiKeyHeader = request.headers.get('X-API-Key');

  return [
    authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null,
    apiKeyHeader,
  ];
};

/**
 * Verifies the API key using the better-auth package
 * Returns the API key information if valid, or null if invalid
 */
export const verifyApiKey = async (request: NextRequest) => {
  try {
    // Extract API key from request headers
    const [bearerToken, apiKey] = extractApiKey(request);

    // We can verify via api key or via bearer token
    if (!bearerToken && !apiKey) {
      return null;
    }

    if (bearerToken) {
      // Let session handle auth
      const session = await auth.api.getSession({
        headers: request.headers,
      });

      if (!session?.user) {
        return null;
      }

      return {
        metadata: {
          organizationId: session.session.activeOrganizationId,
        },
        userId: session.user.id,
      };
    }

    if (apiKey) {
      // Verify API key using better-auth
      const { key, error } = await auth.api.verifyApiKey({
        body: { key: apiKey },
      });

      if (error) {
        log.warn('API key verification error:', { error });
        return null;
      }

      return key;
    }
  } catch (error) {
    log.warn('API key verification error:', { error });
    return null;
  }
};

/**
 * Higher-order function that wraps a route handler with API key authentication
 * If the API key is invalid, returns a 401 Unauthorized response
 * If the API key is valid, calls the original handler with the request and context
 */
export function withApiKeyAuth<T = unknown>(handler: RouteHandler<T>) {
  return withCors(async (req: NextRequest, context: { params: Promise<T> }) => {
    // Skip authentication for OPTIONS requests (CORS preflight)
    if (req.method === 'OPTIONS') {
      return handler(req, context);
    }

    // Skip authentication for auth routes
    if (req.nextUrl.pathname.startsWith('/api/auth')) {
      return handler(req, context);
    }

    // Skip authentication for webhook routes
    if (req.nextUrl.pathname.startsWith('/api/webhooks')) {
      return handler(req, context);
    }

    // Verify API key
    const apiKey = await verifyApiKey(req);

    if (!apiKey) {
      log.warn('API request rejected: No valid API key provided', {
        path: req.nextUrl.pathname,
        method: req.method,
      });

      return NextResponse.json(
        { error: 'Unauthorized: Valid API key required' },
        { status: 401 }
      );
    }

    // If we have a valid API key, add organization info to headers
    const requestHeaders = new Headers(req.headers);
    if (apiKey.metadata?.organizationId) {
      requestHeaders.set('x-organization-id', apiKey.metadata.organizationId);
    }
    requestHeaders.set('x-user-id', apiKey.userId);

    // Create a new request with the modified headers
    const newRequest = new NextRequest(req.url, {
      method: req.method,
      headers: requestHeaders,
      body: req.body,
      cache: req.cache,
      credentials: req.credentials,
      integrity: req.integrity,
      keepalive: req.keepalive,
      mode: req.mode,
      redirect: req.redirect,
      referrer: req.referrer,
      referrerPolicy: req.referrerPolicy,
    });

    // Call the original handler with the new request
    return handler(newRequest, context);
  });
}

/**
 * Fetches data from the specified API with JWT authentication
 * @param path The API path to fetch from
 * @param options Request options
 * @param requestHeaders Optional headers from the incoming request to forward
 */
export const fetchWithAuth = async <T>(
  path: string,
  options: RequestInit = {},
  requestHeaders?: Headers
): Promise<T> => {
  try {
    // Prepare headers for the outgoing request
    const headers: Record<string, string> = {
      'X-Invois-Key': env.CORE_OUTGOING_API_KEY,
      'Content-Type': 'application/json',
      ...((options.headers as Record<string, string>) || {}),
    };

    // Forward x-user-id if available from the incoming request
    if (requestHeaders?.has('x-user-id')) {
      headers['x-user-id'] = requestHeaders.get('x-user-id') || '';
    }

    const response = await fetch(env.CORE_BASE_URL + path, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      log.warn(`API Error: ${path}`, { errorData });

      throw errorData;
    }

    return await response.json();
  } catch (error) {
    log.warn('fetchWithAuth exception', { error, path });
    throw error;
  }
};
