import { fetchWithAuth, withA<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/app/lib/auth';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { type NextRequest, NextResponse } from 'next/server';

const getHandler = async (request: NextRequest) => {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const page = Number.parseInt(searchParams.get('page') || '1', 10);
  const perPage = Number.parseInt(searchParams.get('per_page') || '10', 10);

  const response = await fetchWithAuth(
    urlSerialize('/api/v1/orders', { page, per_page: perPage, query }),
    {},
    request.headers
  );

  return NextResponse.json(response, {
    status: 200,
  });
};

export const OPTIONS = withApiKeyAuth(getHandler);
export const GET = withApiKeyAuth(getHandler);
