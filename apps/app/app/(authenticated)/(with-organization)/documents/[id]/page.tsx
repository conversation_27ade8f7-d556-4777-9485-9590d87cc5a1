import type { EInvoiceV1 } from '@/app/types';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { getSubmittedDocument } from '../actions';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const { data: document } = await getSubmittedDocument(id);

  const title = 'MyInvoice - Submitted Document';
  const description = `Details for ${document.invoiceCode}`;

  return {
    title,
    description,
  };
}

export default async function SubmittedDocumentDetailsPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const { data: document } = await getSubmittedDocument(id);

  if (!document) {
    notFound();
  }

  // Helper function to determine badge variant based on status
  const getStatusVariant = (
    status: string
  ):
    | 'default'
    | 'secondary'
    | 'destructive'
    | 'outline'
    | 'success'
    | 'warning' => {
    switch (status) {
      case 'Valid':
        return 'success';
      case 'Invalid':
      case 'InvalidOrder':
        return 'destructive';
      case 'Pending':
        return 'warning';
      case 'Cancelled':
        return 'secondary';
      default:
        return 'default';
    }
  };

  // TODO: need to check different document type
  // invoice, credit note, debit note
  const documentDetails = document.documentDetails as EInvoiceV1;

  return (
    <>
      <Header pages={['Submitted Documents']} page={document.invoiceCode} />

      <main className="flex-1 space-y-6 px-4 lg:px-8">
        <PageHeader>
          <PageHeaderHeading>Document Details</PageHeaderHeading>
          <PageHeaderDescription>
            View submitted document information
          </PageHeaderDescription>
        </PageHeader>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Document Information</span>
                <Badge variant={getStatusVariant(document.status)}>
                  {document.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Invoice Number
                  </dt>
                  <dd className="text-sm">{document.invoiceCode}</dd>
                </div>
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    UUID
                  </dt>
                  <dd className="text-sm">{document.uuid || 'N/A'}</dd>
                </div>
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Submitted At
                  </dt>
                  <dd className="text-sm">
                    {new Date(document.created_at).toLocaleString()}
                  </dd>
                </div>
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Last Updated
                  </dt>
                  <dd className="text-sm">
                    {new Date(document.updated_at).toLocaleString()}
                  </dd>
                </div>
                {document.failReason && (
                  <div className="col-span-2">
                    <dt className="font-medium text-destructive text-sm">
                      Failure Reason
                    </dt>
                    <dd className="text-destructive text-sm">
                      {document.failReason}
                    </dd>
                  </div>
                )}
              </dl>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Seller
                  </dt>
                  <dd className="text-sm">{documentDetails.supplier.name}</dd>
                </div>
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Seller Tax ID
                  </dt>
                  <dd className="text-sm">{documentDetails.supplier.tin}</dd>
                </div>
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Buyer
                  </dt>
                  <dd className="text-sm">{documentDetails.buyer.name}</dd>
                </div>
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Buyer Tax ID
                  </dt>
                  <dd className="text-sm">
                    {documentDetails.buyer.tin || 'N/A'}
                  </dd>
                </div>
                {documentDetails.invoiceDateTime && (
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Issue Date
                    </dt>
                    <dd className="text-sm">
                      {documentDetails.invoiceDateTime.date}
                    </dd>
                  </div>
                )}
                {documentDetails.billingPeriod && (
                  <>
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Billing Start Date
                      </dt>
                      <dd className="text-sm">
                        {documentDetails.billingPeriod.startDate}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Billing End Date
                      </dt>
                      <dd className="text-sm">
                        {documentDetails.billingPeriod.endDate}
                      </dd>
                    </div>
                  </>
                )}
                {'legalMonetaryTotal' in documentDetails && (
                  <>
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Total Amount (Excl. Tax)
                      </dt>
                      <dd className="text-sm">
                        {documentDetails.legalMonetaryTotal.excludingTax.toLocaleString()}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Total Amount (Incl. Tax)
                      </dt>
                      <dd className="text-sm">
                        {documentDetails.legalMonetaryTotal.includingTax.toLocaleString()}
                      </dd>
                    </div>
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Payable Amount
                      </dt>
                      <dd className="text-sm">
                        {documentDetails.legalMonetaryTotal.payableAmount.toLocaleString()}
                      </dd>
                    </div>
                  </>
                )}
              </dl>
            </CardContent>
          </Card>
        </div>

        {'lineItems' in documentDetails && documentDetails.lineItems && (
          <Card>
            <CardHeader>
              <CardTitle>Line Items</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Quantity</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Tax Amount</TableHead>
                    <TableHead>Classifications</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documentDetails.lineItems.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell className="text-right">
                        {item.unit.count}
                      </TableCell>
                      <TableCell className="text-right">
                        {item.unit.price.toLocaleString()}
                      </TableCell>
                      <TableCell className="text-right">
                        {item.taxAmount.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {item.classifications.map((code, i) => (
                            <Badge
                              key={i}
                              variant="outline"
                              className="text-xs"
                            >
                              {code}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
      </main>
    </>
  );
}
