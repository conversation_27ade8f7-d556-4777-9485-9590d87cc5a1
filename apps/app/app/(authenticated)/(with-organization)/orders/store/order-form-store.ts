'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { OrderFormData } from '../schemas/order-form-schema';

interface OrderFormState {
  // Current step in the multi-step form
  currentStep: number;
  setCurrentStep: (step: number) => void;

  // Form data
  formData: Partial<OrderFormData>;
  updateFormData: (data: Partial<OrderFormData>) => void;
  resetFormData: () => void;

  // Form completion status
  isBasicInfoComplete: boolean;
  setBasicInfoComplete: (complete: boolean) => void;

  isLineItemsComplete: boolean;
  setLineItemsComplete: (complete: boolean) => void;

  isAdditionalInfoComplete: boolean;
  setAdditionalInfoComplete: (complete: boolean) => void;
}

// Initial form data
const initialFormData: Partial<OrderFormData> = {
  isReady: true,
  isConsolidate: false,
  invoiceCode: '',
  invoiceDateTime: new Date(),
  lineItems: [
    {
      id: '',
      classifications: [],
      description: '',
      unit: {
        price: 0,
        count: 1,
        code: undefined,
      },
      taxDetails: [
        {
          taxType: '06', // Default to Not Applicable
          taxRate: {
            percentage: 0,
          },
          taxExemptionReason: '',
        },
      ],
      allowanceCharges: [],
      tarriffCode: '',
      originCountry: 'MYS', // Default to Malaysia
    },
  ],
  payment: undefined,
  buyerName: undefined,
  buyerTin: undefined,
  registrationType: undefined,
  registrationNumber: undefined,
  sstRegistrationNumber: undefined,
  email: undefined,
  address: undefined,
  contactNumber: undefined,
  deliveryDetails: undefined,
  foreignCurrency: undefined,
  billingPeriod: undefined,
  prePayment: undefined,
  billingReferenceNumber: '',
  additionalDocumentReference: [],
};

export const useOrderFormStore = create<OrderFormState>()(
  persist(
    (set) => ({
      // Step management
      currentStep: 0,
      setCurrentStep: (step) => set({ currentStep: step }),

      // Form data management
      formData: initialFormData,
      updateFormData: (data) =>
        set((state) => ({
          formData: {
            ...state.formData,
            ...data,
          },
        })),
      resetFormData: () => set({ formData: initialFormData }),

      // Completion status
      isBasicInfoComplete: false,
      setBasicInfoComplete: (complete) =>
        set({ isBasicInfoComplete: complete }),

      isLineItemsComplete: false,
      setLineItemsComplete: (complete) =>
        set({ isLineItemsComplete: complete }),

      isAdditionalInfoComplete: false,
      setAdditionalInfoComplete: (complete) =>
        set({ isAdditionalInfoComplete: complete }),
    }),
    {
      name: 'order-form-storage',
    }
  )
);
