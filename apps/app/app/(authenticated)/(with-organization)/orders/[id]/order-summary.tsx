'use client';

import type {
  EInvoiceV1,
  LineItem,
  Order,
  SubmittedDocument,
} from '@/app/types';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import { classificationCodesService } from '../schemas/classifications';
import { PaymentModes } from '../schemas/payment-modes';
import { taxTypesService } from '../schemas/tax-types';
import { unitTypesService } from '../schemas/unit-types';

interface OrderSummaryProps {
  order?: Order;
  documents?: SubmittedDocument[];
}

export default function OrderSummary({ order, documents }: OrderSummaryProps) {
  // If no order is provided, show a loading state
  if (!order) {
    return (
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Order Information</CardTitle>
          </CardHeader>
          <CardContent>Loading order information...</CardContent>
        </Card>
      </div>
    );
  }

  // Get the first document with details if available
  const documentWithDetails = documents?.find(
    (doc): doc is SubmittedDocument & { documentDetails: EInvoiceV1 } =>
      doc.status !== 'InvalidOrder' &&
      'documentDetails' in doc &&
      doc.documentDetails !== null &&
      'lineItems' in doc.documentDetails
  );

  const invoiceDetails = documentWithDetails?.documentDetails as
    | EInvoiceV1
    | undefined;

  // Format status for display
  const getStatusBadge = (status: Order['status']) => {
    const statusColors: Record<Order['status'], string> = {
      Pending: 'bg-yellow-100 text-yellow-800',
      Submitted: 'bg-blue-100 text-blue-800',
      Cancelled: 'bg-red-100 text-red-800',
      Valid: 'bg-green-100 text-green-800',
      Invalid: 'bg-red-100 text-red-800',
      InvalidOrder: 'bg-red-100 text-red-800',
    };

    return <Badge className={statusColors[status]}>{status}</Badge>;
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-MY', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (_) {
      return dateString;
    }
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
    }).format(amount);
  };

  // Get classification description
  const getClassificationDescription = (code: string) => {
    const classification = classificationCodesService.getByCode(code);
    return classification ? classification.Description : code;
  };

  // Get tax type description
  const getTaxTypeDescription = (code: string) => {
    const taxType = taxTypesService.getByCode(code);
    return taxType ? taxType.description : code;
  };

  // Get unit type description
  const getUnitTypeDescription = (code: string | undefined) => {
    if (!code) {
      return '';
    }
    const unitType = unitTypesService.getByCode(code);
    return unitType ? unitType.Name : code;
  };

  // Get payment mode description
  const getPaymentModeDescription = (code: string) => {
    const paymentMode = PaymentModes.getByCode(code);
    return paymentMode ? paymentMode.description : code;
  };

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Order Information */}
      <Card>
        <CardHeader>
          <CardTitle>Order Information</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Invoice Code
              </dt>
              <dd className="mt-1">{order.invoice_code}</dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Status
              </dt>
              <dd className="mt-1">{getStatusBadge(order.status)}</dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Created At
              </dt>
              <dd className="mt-1">{formatDate(order.created_at)}</dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Updated At
              </dt>
              <dd className="mt-1">{formatDate(order.updated_at)}</dd>
            </div>
          </dl>
        </CardContent>
      </Card>

      {/* Seller Information */}
      <Card>
        <CardHeader>
          <CardTitle>Seller Information</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-2">
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Name
              </dt>
              <dd className="mt-1">{order.seller_name}</dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">TIN</dt>
              <dd className="mt-1">{order.seller_tin}</dd>
            </div>
            {invoiceDetails?.supplier && (
              <>
                {invoiceDetails.supplier.address && (
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Address
                    </dt>
                    <dd className="mt-1">
                      {invoiceDetails.supplier.address.addressLine0}
                      {invoiceDetails.supplier.address.addressLine1 && (
                        <>, {invoiceDetails.supplier.address.addressLine1}</>
                      )}
                      {invoiceDetails.supplier.address.addressLine2 && (
                        <>, {invoiceDetails.supplier.address.addressLine2}</>
                      )}
                      <br />
                      {invoiceDetails.supplier.address.cityName},{' '}
                      {invoiceDetails.supplier.address.state},{' '}
                      {invoiceDetails.supplier.address.postalZone}
                      <br />
                      {invoiceDetails.supplier.address.country}
                    </dd>
                  </div>
                )}
                {invoiceDetails.supplier.contactNumber && (
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Contact Number
                    </dt>
                    <dd className="mt-1">
                      {invoiceDetails.supplier.contactNumber}
                    </dd>
                  </div>
                )}
                {invoiceDetails.supplier.email && (
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Email
                    </dt>
                    <dd className="mt-1">{invoiceDetails.supplier.email}</dd>
                  </div>
                )}
              </>
            )}
          </dl>
        </CardContent>
      </Card>

      {/* Buyer Information */}
      {(order.buyer_name || invoiceDetails?.buyer) && (
        <Card>
          <CardHeader>
            <CardTitle>Buyer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Name
                </dt>
                <dd className="mt-1">
                  {order.buyer_name || invoiceDetails?.buyer.name}
                </dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  TIN
                </dt>
                <dd className="mt-1">
                  {order.buyer_tin || invoiceDetails?.buyer.tin}
                </dd>
              </div>
              {invoiceDetails?.buyer && (
                <>
                  {invoiceDetails.buyer.registrationType && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Registration Type
                      </dt>
                      <dd className="mt-1">
                        {invoiceDetails.buyer.registrationType}
                      </dd>
                    </div>
                  )}
                  {invoiceDetails.buyer.registrationNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Registration Number
                      </dt>
                      <dd className="mt-1">
                        {invoiceDetails.buyer.registrationNumber}
                      </dd>
                    </div>
                  )}
                  {invoiceDetails.buyer.sstRegistrationNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        SST Registration Number
                      </dt>
                      <dd className="mt-1">
                        {invoiceDetails.buyer.sstRegistrationNumber}
                      </dd>
                    </div>
                  )}
                  {invoiceDetails.buyer.address && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Address
                      </dt>
                      <dd className="mt-1">
                        {invoiceDetails.buyer.address.addressLine0}
                        {invoiceDetails.buyer.address.addressLine1 && (
                          <>, {invoiceDetails.buyer.address.addressLine1}</>
                        )}
                        {invoiceDetails.buyer.address.addressLine2 && (
                          <>, {invoiceDetails.buyer.address.addressLine2}</>
                        )}
                        <br />
                        {invoiceDetails.buyer.address.cityName},{' '}
                        {invoiceDetails.buyer.address.state},{' '}
                        {invoiceDetails.buyer.address.postalZone}
                        <br />
                        {invoiceDetails.buyer.address.country}
                      </dd>
                    </div>
                  )}
                  {invoiceDetails.buyer.contactNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Contact Number
                      </dt>
                      <dd className="mt-1">
                        {invoiceDetails.buyer.contactNumber}
                      </dd>
                    </div>
                  )}
                  {invoiceDetails.buyer.email && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Email
                      </dt>
                      <dd className="mt-1">{invoiceDetails.buyer.email}</dd>
                    </div>
                  )}
                </>
              )}
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Invoice Details */}
      {invoiceDetails && (
        <Card>
          <CardHeader>
            <CardTitle>Invoice Details</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Invoice Number
                </dt>
                <dd className="mt-1">{invoiceDetails.invoiceCode}</dd>
              </div>
              {invoiceDetails.invoiceDateTime && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Invoice Date
                  </dt>
                  <dd className="mt-1">
                    {invoiceDetails.invoiceDateTime.date}{' '}
                    {invoiceDetails.invoiceDateTime.time}
                  </dd>
                </div>
              )}
              {invoiceDetails.payment && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Payment Mode
                  </dt>
                  <dd className="mt-1">
                    {getPaymentModeDescription(invoiceDetails.payment.mode)}
                  </dd>
                </div>
              )}
              {invoiceDetails.payment?.terms && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Payment Terms
                  </dt>
                  <dd className="mt-1">{invoiceDetails.payment.terms}</dd>
                </div>
              )}
              {invoiceDetails.foreignCurrency && (
                <>
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Currency
                    </dt>
                    <dd className="mt-1">
                      {invoiceDetails.foreignCurrency.currencyCode}
                    </dd>
                  </div>
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Exchange Rate
                    </dt>
                    <dd className="mt-1">
                      {invoiceDetails.foreignCurrency.currencyExchangeRate}
                    </dd>
                  </div>
                </>
              )}
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Line Items */}
      {invoiceDetails?.lineItems && invoiceDetails.lineItems.length > 0 && (
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle>Line Items</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Tax</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoiceDetails.lineItems.map(
                  (item: LineItem, index: number) => (
                    <TableRow key={item.id || index}>
                      <TableCell className="font-medium">
                        {item.description}
                        <div className="mt-1 text-muted-foreground text-xs">
                          Classifications:{' '}
                          {item.classifications
                            .map((code) => getClassificationDescription(code))
                            .join(', ')}
                        </div>
                      </TableCell>
                      <TableCell>
                        {item.unit.count}{' '}
                        {getUnitTypeDescription(item.unit.code)}
                      </TableCell>
                      <TableCell>{formatCurrency(item.unit.price)}</TableCell>
                      <TableCell>
                        {item.taxDetails.map((tax, i) => (
                          <div key={i} className="text-sm">
                            {getTaxTypeDescription(tax.taxType)}
                            {tax.taxRate.percentage &&
                              ` (${tax.taxRate.percentage}%)`}
                            {tax.taxRate.ratePerUnit &&
                              ` (${tax.taxRate.ratePerUnit} per unit)`}
                          </div>
                        ))}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.unit.price * item.unit.count)}
                      </TableCell>
                    </TableRow>
                  )
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Totals */}
      {invoiceDetails?.legalMonetaryTotal && (
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle>Order Totals</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
              <div className="col-start-1">
                <dt className="font-medium text-muted-foreground">
                  Subtotal (excluding tax)
                </dt>
                <dd className="mt-1">
                  {formatCurrency(
                    invoiceDetails.legalMonetaryTotal.excludingTax
                  )}
                </dd>
              </div>

              {invoiceDetails.legalMonetaryTotal.discountValue &&
                invoiceDetails.legalMonetaryTotal.discountValue > 0 && (
                  <div className="col-start-1">
                    <dt className="font-medium text-muted-foreground">
                      Discount
                    </dt>
                    <dd className="mt-1">
                      -
                      {formatCurrency(
                        invoiceDetails.legalMonetaryTotal.discountValue
                      )}
                    </dd>
                  </div>
                )}

              {invoiceDetails.legalMonetaryTotal.feeAmount &&
                invoiceDetails.legalMonetaryTotal.feeAmount > 0 && (
                  <div className="col-start-1">
                    <dt className="font-medium text-muted-foreground">
                      Additional Fees
                    </dt>
                    <dd className="mt-1">
                      {formatCurrency(
                        invoiceDetails.legalMonetaryTotal.feeAmount
                      )}
                    </dd>
                  </div>
                )}

              <div className="col-start-1">
                <dt className="font-medium text-muted-foreground">
                  Tax Amount
                </dt>
                <dd className="mt-1">
                  {formatCurrency(
                    invoiceDetails.legalMonetaryTotal.includingTax -
                      invoiceDetails.legalMonetaryTotal.excludingTax
                  )}
                </dd>
              </div>

              <div className="col-start-1 border-t pt-2">
                <dt className="font-medium">Total Amount</dt>
                <dd className="mt-1 font-bold">
                  {formatCurrency(
                    invoiceDetails.legalMonetaryTotal.payableAmount
                  )}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Additional Document References */}
      {invoiceDetails?.additionalDocumentReference &&
        invoiceDetails.additionalDocumentReference.length > 0 && (
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>Additional Document References</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Reference ID</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoiceDetails.additionalDocumentReference.map(
                    (ref, index) => (
                      <TableRow key={index}>
                        <TableCell>{ref.id}</TableCell>
                        <TableCell>{ref.type || 'N/A'}</TableCell>
                        <TableCell>{ref.description || 'N/A'}</TableCell>
                      </TableRow>
                    )
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
    </div>
  );
}
