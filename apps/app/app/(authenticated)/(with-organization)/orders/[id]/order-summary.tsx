"use client";

import type {
  EInvoiceV1,
  LineItem,
  Order,
  SubmittedDocument,
} from "@/app/types";
import { Badge } from "@repo/design-system/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@repo/design-system/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/design-system/components/ui/table";
import { classificationCodesService } from "../schemas/classifications";
import { PaymentModes } from "../schemas/payment-modes";
import { taxTypesService } from "../schemas/tax-types";
import { unitTypesService } from "../schemas/unit-types";

interface OrderSummaryProps {
  order?: Order;
}

export default function OrderSummary({ order }: OrderSummaryProps) {
  // If no order is provided, show a loading state
  if (!order) {
    return (
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Order Information</CardTitle>
          </CardHeader>
          <CardContent>Loading order information...</CardContent>
        </Card>
      </div>
    );
  }

  // Get the first document with details if available
  const documentWithDetails =
    order.submitted_documents.length > 0 ? order.submitted_documents[0] : null;

  // Format status for display
  const getStatusBadge = (status: Order["status"]) => {
    const statusColors: Record<Order["status"], string> = {
      Draft: "bg-gray-100 text-gray-800",
      Pending: "bg-yellow-100 text-yellow-800",
      Submitted: "bg-blue-100 text-blue-800",
    };

    return <Badge className={statusColors[status]}>{status}</Badge>;
  };

  const getStatusBadgeSubmittedDocument = (
    status: SubmittedDocument["status"]
  ) => {
    const statusColors: Record<SubmittedDocument["status"], string> = {
      Submitted: "bg-blue-100 text-blue-800",
      Cancelled: "bg-red-100 text-red-800",
      Valid: "bg-green-100 text-green-800",
      Invalid: "bg-red-100 text-red-800",
    };

    return <Badge className={statusColors[status]}>{status}</Badge>;
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString("en-MY", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (_) {
      return dateString;
    }
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-MY", {
      style: "currency",
      currency: "MYR",
    }).format(amount);
  };

  // Get classification description
  const getClassificationDescription = (code: string) => {
    const classification = classificationCodesService.getByCode(code);
    return classification ? classification.Description : code;
  };

  // Get tax type description
  const getTaxTypeDescription = (code: string) => {
    const taxType = taxTypesService.getByCode(code);
    return taxType ? taxType.description : code;
  };

  // Get unit type description
  const getUnitTypeDescription = (code: string | undefined) => {
    if (!code) {
      return "";
    }
    const unitType = unitTypesService.getByCode(code);
    return unitType ? unitType.Name : code;
  };

  // Get payment mode description
  const getPaymentModeDescription = (code: string) => {
    const paymentMode = PaymentModes.getByCode(code);
    return paymentMode ? paymentMode.description : code;
  };

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Order Information */}
      <Card>
        <CardHeader>
          <CardTitle>Order Information</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Invoice Code
              </dt>
              <dd className="mt-1">{order.invoice_code}</dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Status
              </dt>
              <dd className="mt-1">
                {documentWithDetails
                  ? getStatusBadgeSubmittedDocument(documentWithDetails.status)
                  : getStatusBadge(order.status)}
              </dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Order is Ready for submission
              </dt>
              <dd className="mt-1">
                <Badge
                  className={` ${order.is_ready ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                >
                  {order.is_ready ? "Yes" : "No"}
                </Badge>
              </dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Created At
              </dt>
              <dd className="mt-1">{formatDate(order.created_at)}</dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Updated At
              </dt>
              <dd className="mt-1">{formatDate(order.updated_at)}</dd>
            </div>
          </dl>
        </CardContent>
      </Card>

      {/* Buyer Information */}
      {order.buyer && (
        <Card>
          <CardHeader>
            <CardTitle>Buyer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Name
                </dt>
                <dd className="mt-1">{order.buyer.name}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  TIN
                </dt>
                <dd className="mt-1">{order.buyer.tin}</dd>
              </div>
              {order?.buyer && (
                <>
                  {order.buyer.registrationType && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Registration Type
                      </dt>
                      <dd className="mt-1">{order.buyer.registrationType}</dd>
                    </div>
                  )}
                  {order.buyer.registrationNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Registration Number
                      </dt>
                      <dd className="mt-1">{order.buyer.registrationNumber}</dd>
                    </div>
                  )}
                  {order.buyer.sstRegistrationNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        SST Registration Number
                      </dt>
                      <dd className="mt-1">
                        {order.buyer.sstRegistrationNumber}
                      </dd>
                    </div>
                  )}
                  {order.buyer.address && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Address
                      </dt>
                      <dd className="mt-1">
                        {order.buyer.address.addressLine0}
                        {order.buyer.address.addressLine1 && (
                          <>, {order.buyer.address.addressLine1}</>
                        )}
                        {order.buyer.address.addressLine2 && (
                          <>, {order.buyer.address.addressLine2}</>
                        )}
                        <br />
                        {order.buyer.address.cityName},{" "}
                        {order.buyer.address.state},{" "}
                        {order.buyer.address.postalZone}
                        <br />
                        {order.buyer.address.country}
                      </dd>
                    </div>
                  )}
                  {order.buyer.contactNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Contact Number
                      </dt>
                      <dd className="mt-1">{order.buyer.contactNumber}</dd>
                    </div>
                  )}
                  {order.buyer.email && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Email
                      </dt>
                      <dd className="mt-1">{order.buyer.email}</dd>
                    </div>
                  )}
                </>
              )}
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Invoice Details */}
      {order && (
        <Card>
          <CardHeader>
            <CardTitle>Invoice Details</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Invoice Number
                </dt>
                <dd className="mt-1">{order.invoice_code}</dd>
              </div>
              {order.invoice_date_time && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Invoice Date
                  </dt>
                  <dd className="mt-1">
                    {formatDate(order.invoice_date_time.date)}
                  </dd>
                </div>
              )}
              {order.payment && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Payment Mode
                  </dt>
                  <dd className="mt-1">
                    {getPaymentModeDescription(order.payment.mode)}
                  </dd>
                </div>
              )}
              {order.payment?.terms && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Payment Terms
                  </dt>
                  <dd className="mt-1">{order.payment.terms}</dd>
                </div>
              )}
              {order.foreign_currency && (
                <>
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Currency
                    </dt>
                    <dd className="mt-1">
                      {order.foreign_currency.currencyCode}
                    </dd>
                  </div>
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Exchange Rate
                    </dt>
                    <dd className="mt-1">
                      {order.foreign_currency.currencyExchangeRate}
                    </dd>
                  </div>
                </>
              )}
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Line Items */}
      {order?.line_items && order.line_items.length > 0 && (
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle>Line Items</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Tax</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.line_items.map((item: LineItem, index: number) => (
                  <TableRow key={item.id || index}>
                    <TableCell className="font-medium">
                      {item.description}
                      <div className="mt-1 text-muted-foreground text-xs">
                        Classifications:{" "}
                        {item.classifications
                          .map((code) => getClassificationDescription(code))
                          .join(", ")}
                      </div>
                    </TableCell>
                    <TableCell>
                      {item.unit.count} {getUnitTypeDescription(item.unit.code)}
                    </TableCell>
                    <TableCell>{formatCurrency(item.unit.price)}</TableCell>
                    <TableCell>
                      {item.taxDetails.map((tax, i) => (
                        <div key={i} className="text-sm">
                          {getTaxTypeDescription(tax.taxType)}
                          {tax.taxRate.percentage &&
                            ` (${tax.taxRate.percentage}%)`}
                          {tax.taxRate.ratePerUnit &&
                            ` (${tax.taxRate.ratePerUnit} per unit)`}
                        </div>
                      ))}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(item.unit.price * item.unit.count)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Totals */}
      {order?.legal_monetary_total && (
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle>Order Totals</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
              <div className="col-start-1">
                <dt className="font-medium text-muted-foreground">
                  Subtotal (excluding tax)
                </dt>
                <dd className="mt-1">
                  {formatCurrency(order.legal_monetary_total.excludingTax)}
                </dd>
              </div>

              {order.legal_monetary_total.discountValue &&
                order.legal_monetary_total.discountValue > 0 && (
                  <div className="col-start-1">
                    <dt className="font-medium text-muted-foreground">
                      Discount
                    </dt>
                    <dd className="mt-1">
                      -
                      {formatCurrency(order.legal_monetary_total.discountValue)}
                    </dd>
                  </div>
                )}

              {order.legal_monetary_total.feeAmount &&
                order.legal_monetary_total.feeAmount > 0 && (
                  <div className="col-start-1">
                    <dt className="font-medium text-muted-foreground">
                      Additional Fees
                    </dt>
                    <dd className="mt-1">
                      {formatCurrency(order.legal_monetary_total.feeAmount)}
                    </dd>
                  </div>
                )}

              <div className="col-start-1">
                <dt className="font-medium text-muted-foreground">
                  Tax Amount
                </dt>
                <dd className="mt-1">
                  {formatCurrency(
                    order.legal_monetary_total.includingTax -
                      order.legal_monetary_total.excludingTax
                  )}
                </dd>
              </div>

              <div className="col-start-1 border-t pt-2">
                <dt className="font-medium">Total Amount</dt>
                <dd className="mt-1 font-bold">
                  {formatCurrency(order.legal_monetary_total.payableAmount)}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Additional Document References */}
      {order?.additional_document_reference &&
        order.additional_document_reference.length > 0 && (
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>Additional Document References</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Reference ID</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {order.additional_document_reference.map((ref, index) => (
                    <TableRow key={index}>
                      <TableCell>{ref.id}</TableCell>
                      <TableCell>{ref.type || "N/A"}</TableCell>
                      <TableCell>{ref.description || "N/A"}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
    </div>
  );
}
