import type { SubmittedDocument } from '@/app/types';
import {} from '@repo/design-system/components/ui/card';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import type { ReactElement } from 'react';
import { getOrder } from '../actions';
import { getOrderDocuments } from '../actions/get-order-documents';
import OrderSummary from './order-summary';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const { data: order } = await getOrder(id);

  const title = 'MyInvoice - Orders';
  const description = `Details for ${order.invoice_code}`;

  return {
    title,
    description,
  };
}

export default async function UserDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const { data: order } = await getOrder(id);

  if (!order) {
    notFound();
  }

  // Fetch documents for this order
  let documents: SubmittedDocument[] = [];
  try {
    const response = await getOrderDocuments(id);
    documents = response.data;
  } catch (error) {
    console.error('Failed to fetch order documents:', error);
  }

  return <OrderSummary order={order} documents={documents} />;
}
