import { Tab<PERSON>ink, TabLinkList } from '@/app/components/tab-links';
import {} from '@repo/design-system/components/ui/card';
import {
  PageHeader,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import type { ReactElement, ReactNode } from 'react';
import { Header } from '../../components/header';
import { getOrder } from '../actions';

type PageProps = {
  children: ReactNode;
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const { data: order } = await getOrder(id);

  const title = 'MyInvoice - Orders';
  const description = `Details for ${order.invoice_code}`;

  return {
    title,
    description,
  };
}

export default async function UserDetailPage({
  children,
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const { data: order } = await getOrder(id);

  if (!order) {
    notFound();
  }

  return (
    <>
      <Header pages={['Orders']} page={order.invoice_code} />

      <main className="flex-1 space-y-4 px-4 lg:px-8">
        <PageHeader>
          <div className="space-y-2">
            <PageHeaderHeading>{order.invoice_code}</PageHeaderHeading>
            <TabLinkList className="mt-4">
              <TabLink href={`/orders/${id}`}>Summary</TabLink>
              <TabLink href={`/orders/${id}/documents`}>Documents</TabLink>
            </TabLinkList>
          </div>
        </PageHeader>

        <div className="md:min-h-min">
          <div className="@container/main flex-1">{children}</div>
        </div>
      </main>
    </>
  );
}
