// apps/app/app/(authenticated)/(with-organization)/orders/components/submit-button.tsx
"use client";

import { Button } from "@repo/design-system/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@repo/design-system/components/ui/tooltip";
import type { OrderFormData } from "../schemas/order-form-schema";
import { useRouter } from "next/navigation";
import { useSubmitOrder } from "../hooks/useSubmitOrder";
import { toast } from "@repo/design-system/components/ui/sonner";

interface SubmitButtonProps {
  status: string;
  order_id: number;
}

export function SubmitButton({ status, order_id }: SubmitButtonProps) {
  const router = useRouter();
  const { submitOrderToMyInvois, isLoading } = useSubmitOrder();

  // Handle form submission
  const handleSubmit = async () => {
    try {
      await submitOrderToMyInvois(order_id);
      toast.success("Order submitted to MyInvois successfully");

      router.push("/orders");
    } catch (error) {
      console.error("Failed to create order:", error);
      toast.error("Failed to create order. Please try again.");
    }
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div>
          <Button
            variant="default"
            size="lg"
            type="button"
            onClick={handleSubmit}
            disabled={isLoading || status != "Pending"}
          >
            {isLoading ? "Submitting..." : "Submit to MyInvois"}
          </Button>
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>
          {status == "Pending"
            ? "Submit to MyInvois"
            : status == "Draft"
              ? "Please set the ready status of the order to true before submitting to MyInvois"
              : status == "Submitted"
                ? "Order has already been submitted to MyInvois and is pending for validation"
                : "Unknown status"}
        </p>
      </TooltipContent>
    </Tooltip>
  );
}
