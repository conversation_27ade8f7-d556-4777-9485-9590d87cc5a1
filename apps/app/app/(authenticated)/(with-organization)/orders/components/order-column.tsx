"use client";

import type { Order } from "@/app/types";
import type { ColumnDef } from "@tanstack/react-table";

export const columns: ColumnDef<Order>[] = [
  // {
  //   accessorKey: 'id',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="ghost"
  //         size="sm"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         Order ID
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  // },
  {
    accessorKey: "invoice_code",
    header: "Invoice Number",
  },
  {
    accessorKey: "buyer",
    header: "Customer",
    cell: ({ row }) => row.original.buyer.name,
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "created_at",
    header: "Created At",
    cell: ({ row }) => new Date(row.original.created_at).toLocaleString(),
  },
];
