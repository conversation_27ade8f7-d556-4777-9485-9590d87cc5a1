'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { toast } from '@repo/design-system/components/ui/sonner';
import { formatDateTime } from '@repo/design-system/lib/format';
import { log } from '@repo/observability/log';
import { useRouter } from 'next/navigation';
import { useCreateOrder } from '../../hooks/useCreateOrder';
import type { OrderFormData } from '../../schemas/order-form-schema';
import { useOrderFormStore } from '../../store/order-form-store';

interface ReviewStepProps {
  onBack: () => void;
  onSuccess?: () => void;
}

export function ReviewStep({ onBack, onSuccess }: ReviewStepProps) {
  const router = useRouter();
  const { createOrderWithSubmittedDocument, isLoading } = useCreateOrder();
  const { formData, setCurrentStep, resetFormData } = useOrderFormStore();

  // Handle form submission
  const handleSubmit = async () => {
    try {
      await createOrderWithSubmittedDocument(formData as OrderFormData);

      toast.success('Order created successfully');

      setCurrentStep(0);
      resetFormData();

      if (onSuccess) {
        onSuccess();
        return;
      }

      router.push('/orders');
    } catch (error) {
      console.error('Failed to create order:', error);
      toast.error('Failed to create order. Please try again.');
    }
  };

  return (
    <>
      {/* Basic Information Review */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Invoice Code
              </dt>
              <dd className="mt-1">{formData.invoiceCode}</dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Invoice Date/Time
              </dt>
              <dd className="mt-1">
                {formData.invoiceDateTime &&
                  formatDateTime(formData.invoiceDateTime, 'PPP HH:mm:ss')}
              </dd>
            </div>
          </dl>
        </CardContent>
      </Card>

      {/* Line Items Review */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Line Items</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-4">
            {formData.lineItems?.map((item, index) => (
              <div
                key={index}
                className="border-b pb-4 last:border-0 last:pb-0"
              >
                <h4 className="font-medium">Item {index + 1}</h4>
                <dl className="mt-2 grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Description
                    </dt>
                    <dd className="mt-1">{item.description}</dd>
                  </div>
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Price
                    </dt>
                    <dd className="mt-1">
                      {item.unit.count} x {item.unit.price} ={' '}
                      {item.unit.count * item.unit.price}
                    </dd>
                  </div>
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Classifications
                    </dt>
                    <dd className="mt-1">{item.classifications.join(', ')}</dd>
                  </div>
                  <div>
                    <dt className="font-medium text-muted-foreground text-sm">
                      Origin Country
                    </dt>
                    <dd className="mt-1">{item.originCountry}</dd>
                  </div>
                </dl>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Buyer Information Review (if provided) */}
      {(formData.buyerName || formData.buyerTin) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Buyer Information</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
              {formData.buyerName && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Buyer Name
                  </dt>
                  <dd className="mt-1">{formData.buyerName}</dd>
                </div>
              )}
              {formData.buyerTin && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Buyer TIN
                  </dt>
                  <dd className="mt-1">{formData.buyerTin}</dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Payment Information Review (if provided) */}
      {formData.payment && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Payment Information</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Payment Mode
                </dt>
                <dd className="mt-1">
                  {formData.payment.mode === '01' && 'Cash'}
                  {formData.payment.mode === '02' && 'Cheque'}
                  {formData.payment.mode === '03' && 'Bank Transfer'}
                  {formData.payment.mode === '04' && 'Credit Card'}
                  {formData.payment.mode === '05' && 'Debit Card'}
                  {formData.payment.mode === '06' && 'E-Wallet'}
                  {formData.payment.mode === '07' && 'Digital Bank'}
                  {formData.payment.mode === '08' && 'Others'}
                </dd>
              </div>
              {formData.payment.terms && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Payment Terms
                  </dt>
                  <dd className="mt-1">{formData.payment.terms}</dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleSubmit} disabled={isLoading}>
          {isLoading ? 'Creating...' : 'Create Order'}
        </Button>
      </div>
    </>
  );
}
