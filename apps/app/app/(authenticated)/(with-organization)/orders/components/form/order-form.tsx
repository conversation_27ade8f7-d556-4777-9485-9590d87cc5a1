'use client';
import { useEffect } from 'react';
import type { OrderFormData } from '../../schemas/order-form-schema';
import { useOrderFormStore } from '../../store/order-form-store';
import { AdditionalDetailsStep } from './additional-details-step';
import { BasicInfoStep } from './basic-info-step';
import { LineItemsStep } from './line-items-step';
import { OrderFormStepper, orderFormSteps } from './order-form-stepper';
import { ReviewStep } from './review-step';

interface OrderFormProps {
  initialData?: OrderFormData;
  onSuccess?: () => void;
}

export function OrderForm({ initialData, onSuccess }: OrderFormProps) {
  const {
    currentStep,
    formData,
    setCurrentStep,
    updateFormData,
    resetFormData,
  } = useOrderFormStore();

  // Initialize form data with initialData if provided
  useEffect(() => {
    if (initialData) {
      updateFormData(initialData);
    } else {
      resetFormData();
    }
  }, [initialData, updateFormData, resetFormData]);

  // Handle next step
  const handleNext = () => {
    if (currentStep < orderFormSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Handle back step
  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="space-y-6">
      <OrderFormStepper />

      {currentStep === 0 && <BasicInfoStep onNext={handleNext} />}

      {currentStep === 1 && (
        <LineItemsStep onNext={handleNext} onBack={handleBack} />
      )}

      {currentStep === 2 && (
        <AdditionalDetailsStep onNext={handleNext} onBack={handleBack} />
      )}

      {currentStep === 3 && (
        <ReviewStep onBack={handleBack} onSuccess={onSuccess} />
      )}
    </div>
  );
}
