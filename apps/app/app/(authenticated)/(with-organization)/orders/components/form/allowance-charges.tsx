import { Button } from '@repo/design-system/components/ui/button';
import { Checkbox } from '@repo/design-system/components/ui/checkbox';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useFormContext,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { PlusCircle, Trash2 } from 'lucide-react';
import type { AllowanceChargeData } from '../../schemas/order-form-schema';

export function AllowanceCharges({
  lineItemIndex,
  addAllowanceCharge,
  removeAllowanceCharge,
}: {
  lineItemIndex: number;
  addAllowanceCharge: (lineItemIndex: number) => void;
  removeAllowanceCharge: (
    lineItemIndex: number,
    allowanceChargeIndex: number
  ) => void;
}) {
  const form = useFormContext();

  return (
    <div className="mt-6">
      <div className="mb-4 flex items-center justify-between">
        <h5 className="mb-4 font-medium">Allowances & Charges</h5>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => addAllowanceCharge(lineItemIndex)}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Allowance/Charge
        </Button>
      </div>

      {form
        .watch(`lineItems.${lineItemIndex}.allowanceCharges`)
        .map((_: AllowanceChargeData, allowanceChargeIndex: number) => {
          return (
            <div
              key={allowanceChargeIndex}
              className="mb-4 rounded-md border p-4"
            >
              <div className="mb-4 flex items-center justify-between">
                <h3 className="font-medium text-md">
                  Allowance/Charge {allowanceChargeIndex + 1}
                </h3>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    removeAllowanceCharge(lineItemIndex, allowanceChargeIndex)
                  }
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* isCharge */}
                <FormField
                  control={form.control}
                  name={`lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.isCharge`}
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="cursor-pointer font-normal">
                        This is a charge (not an allowance)
                      </FormLabel>
                    </FormItem>
                  )}
                />

                {/* Rate */}
                <FormField
                  control={form.control}
                  name={`lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.rate`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rate (0-0.9)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="0.9"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) =>
                            field.onChange(
                              Number.parseFloat(e.target.value) || 0
                            )
                          }
                          value={field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Reason */}
              <FormField
                control={form.control}
                name={`lineItems.${lineItemIndex}.allowanceCharges.${allowanceChargeIndex}.reason`}
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <FormLabel>Reason</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter reason" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          );
        })}
    </div>
  );
}
