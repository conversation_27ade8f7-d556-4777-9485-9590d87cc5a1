'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import { Card, CardContent } from '@repo/design-system/components/ui/card';
import {
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useFormContext,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import { Trash2 } from 'lucide-react';
import { AllowanceCharges } from './allowance-charges';
import { ClassificationSelector } from './classification-selector';
import { TaxDetails } from './tax-details';
import { UnitCodeSelector } from './unit-code-selector';

interface LineItemProps {
  index: number;
  onRemove: (index: number) => void;
  addTaxDetail: (lineItemIndex: number) => void;
  removeTaxDetail: (lineItemIndex: number, taxDetailIndex: number) => void;
  addAllowanceCharge: (lineItemIndex: number) => void;
  removeAllowanceCharge: (
    lineItemIndex: number,
    allowanceChargeIndex: number
  ) => void;
}

export function LineItemForm({
  index,
  onRemove,
  addTaxDetail,
  removeTaxDetail,
  addAllowanceCharge,
  removeAllowanceCharge,
}: LineItemProps) {
  const form = useFormContext();

  return (
    <Card className="mb-4">
      <CardContent className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <h4 className="font-medium">Line Item {index + 1}</h4>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onRemove(index)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name={`lineItems.${index}.id`}
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>ID</FormLabel>
                <FormControl>
                  <Input placeholder="Enter item id" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`lineItems.${index}.tarriffCode`}
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Tarriff Code (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="Enter tarriff code" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`lineItems.${index}.description`}
            render={({ field }) => (
              <GridFormItem className="col-span-2">
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea placeholder="Enter item description" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`lineItems.${index}.classifications`}
            render={({ field }) => (
              <ClassificationSelector
                value={field.value || []}
                onChange={field.onChange}
              />
            )}
          />

          <FormField
            control={form.control}
            name={`lineItems.${index}.unit.code`}
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Unit Type</FormLabel>
                <UnitCodeSelector
                  value={field.value}
                  onChange={field.onChange}
                  productDescription={form.watch(
                    `lineItems.${index}.description`
                  )}
                />
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`lineItems.${index}.unit.price`}
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Unit Price</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    {...field}
                    onChange={(e) =>
                      field.onChange(Number.parseFloat(e.target.value) || 0)
                    }
                    value={field.value}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`lineItems.${index}.unit.count`}
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Quantity</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    placeholder="1"
                    {...field}
                    onChange={(e) =>
                      field.onChange(Number.parseInt(e.target.value, 10) || 1)
                    }
                    value={field.value}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <GridFormItem>
            <FormLabel>Origin Country</FormLabel>
            {/* <LocationSelector
              defaultCountry={countries.find(
                (country) => country.iso3 === 'MYS'
              )}
              onCountryChange={(country) => {
                if (country) {
                  form.setValue(
                    `lineItems.${index}.originCountry`,
                    country.iso3
                  );
                } else {
                  form.setValue(`lineItems.${index}.originCountry`, '');
                }
              }}
            /> */}
            <FormMessage />
          </GridFormItem>
        </div>

        <TaxDetails
          lineItemIndex={index}
          addTaxDetail={addTaxDetail}
          removeTaxDetails={removeTaxDetail}
        />

        <AllowanceCharges
          lineItemIndex={index}
          addAllowanceCharge={addAllowanceCharge}
          removeAllowanceCharge={removeAllowanceCharge}
        />
      </CardContent>
    </Card>
  );
}
