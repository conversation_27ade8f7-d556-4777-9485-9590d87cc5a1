import { But<PERSON> } from "@repo/design-system/components/ui/button";
import {
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useFormContext,
} from "@repo/design-system/components/ui/form";
import { Input } from "@repo/design-system/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/design-system/components/ui/select";
import { Textarea } from "@repo/design-system/components/ui/textarea";
import { PlusCircle, Trash2 } from "lucide-react";
import type { TaxDetailData } from "../../schemas/order-form-schema";
import { taxTypesService } from "../../schemas/tax-types";
import { useEffect, useState } from "react";

export function TaxDetails({
  lineItemIndex,
  addTaxDetail,
  removeTaxDetails,
}: {
  lineItemIndex: number;
  addTaxDetail: (lineItemIndex: number) => void;
  removeTaxDetails: (lineItemIndex: number, taxDetailIndex: number) => void;
}) {
  const form = useFormContext();

  const [factorTypes, setFactorTypes] = useState<
    ("Percentage" | "RatePerUnit")[]
  >(["Percentage"]);

  let currentTaxDetailIndex = 0;
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      const watchedField = `lineItems.${lineItemIndex}.taxDetails.${currentTaxDetailIndex}.taxType`;

      if (name === watchedField && type === "change") {
        const taxType =
          value.lineItems?.[lineItemIndex]?.taxDetails?.[currentTaxDetailIndex]
            ?.taxType;

        if (taxType === "06") {
          form.setValue(
            `lineItems.${lineItemIndex}.taxDetails.${currentTaxDetailIndex}.taxRate.percentage`,
            0
          );
          form.setValue(
            `lineItems.${lineItemIndex}.taxDetails.${currentTaxDetailIndex}.taxRate.ratePerUnit`,
            0
          );
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, lineItemIndex, currentTaxDetailIndex]);

  return (
    <div className="mt-6">
      <div className="mb-4 flex items-center justify-between">
        <h5 className="font-medium">Tax Details</h5>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => {
            addTaxDetail(lineItemIndex);
            setFactorTypes([...factorTypes, "Percentage"]);
          }}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Tax Detail
        </Button>
      </div>

      {form
        .watch(`lineItems.${lineItemIndex}.taxDetails`)
        .map((_: TaxDetailData, taxDetailIndex: number) => {
          return (
            <div key={taxDetailIndex} className="mb-4 rounded-md border p-4">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="font-medium text-md">
                  Tax {taxDetailIndex + 1}
                </h3>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    removeTaxDetails(lineItemIndex, taxDetailIndex);
                    const latestFactorTypesArr = factorTypes;
                    latestFactorTypesArr.splice(taxDetailIndex, 1);
                    setFactorTypes(latestFactorTypesArr);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Tax Type */}
                <FormField
                  control={form.control}
                  name={`lineItems.${lineItemIndex}.taxDetails.${taxDetailIndex}.taxType`}
                  render={({ field }) => (
                    <GridFormItem>
                      <FormLabel>Tax Type</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          currentTaxDetailIndex = taxDetailIndex;
                          field.onChange(value);
                        }}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select tax type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {taxTypesService.getAllTypes().map((taxType) => (
                            <SelectItem key={taxType.code} value={taxType.code}>
                              {taxType.description}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </GridFormItem>
                  )}
                />

                {/* Percentage or Rate */}
                {form.watch(
                  `lineItems.${lineItemIndex}.taxDetails.${taxDetailIndex}.taxType`
                ) !== "06" && (
                  <FormField
                    name={"factorType"}
                    render={({ field }) => (
                      <GridFormItem>
                        <FormLabel>Multiplication Factor</FormLabel>
                        <Select
                          onValueChange={(
                            value: "Percentage" | "RatePerUnit"
                          ) => {
                            const latestFactorTypesArr = factorTypes;
                            latestFactorTypesArr[taxDetailIndex] = value;
                            setFactorTypes(latestFactorTypesArr);
                            return field.onChange(value);
                          }}
                          value={factorTypes[taxDetailIndex]}
                          defaultValue={factorTypes[taxDetailIndex]}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select factor type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem
                              key={"RatePerUnit"}
                              value={"RatePerUnit"}
                            >
                              Rate Per Unit (eg. 10.50 which mean each unit
                              taxed 10.50)
                            </SelectItem>
                            <SelectItem key={"Percentage"} value={"Percentage"}>
                              Percentage (eg. 6 which represent 0.06)
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </GridFormItem>
                    )}
                  />
                )}

                {/* Tax Rate - Percentage: Hide for 06 (N/A) */}
                {form.watch(
                  `lineItems.${lineItemIndex}.taxDetails.${taxDetailIndex}.taxType`
                ) !== "06" &&
                  factorTypes[taxDetailIndex] == "Percentage" && (
                    <FormField
                      control={form.control}
                      name={`lineItems.${lineItemIndex}.taxDetails.${taxDetailIndex}.taxRate.percentage`}
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Tax Percentage (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => {
                                const value = Number.parseFloat(e.target.value);
                                field.onChange(
                                  Number.isNaN(value) ? undefined : value
                                );
                              }}
                              value={
                                field.value === undefined ? "" : field.value
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  )}

                {/* Tax Rate - Rate Per Unit: Hide for 06 (N/A) */}
                {form.watch(
                  `lineItems.${lineItemIndex}.taxDetails.${taxDetailIndex}.taxType`
                ) !== "06" &&
                  factorTypes[taxDetailIndex] == "RatePerUnit" && (
                    <FormField
                      control={form.control}
                      name={`lineItems.${lineItemIndex}.taxDetails.${taxDetailIndex}.taxRate.ratePerUnit`}
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Rate Per Unit</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => {
                                const value = Number.parseFloat(e.target.value);
                                field.onChange(
                                  Number.isNaN(value) ? undefined : value
                                );
                              }}
                              value={
                                field.value === undefined ? "" : field.value
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  )}

                {/* Tax Exemption Reason - Only shown when tax type is 'E' */}
                {form.watch(
                  `lineItems.${lineItemIndex}.taxDetails.${taxDetailIndex}.taxType`
                ) === "E" && (
                  <FormField
                    control={form.control}
                    name={`lineItems.${lineItemIndex}.taxDetails.${taxDetailIndex}.taxExemptionReason`}
                    render={({ field }) => (
                      <GridFormItem className="col-span-2">
                        <FormLabel>Tax Exemption Reason</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter tax exemption reason"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </GridFormItem>
                    )}
                  />
                )}
              </div>
            </div>
          );
        })}
    </div>
  );
}
