'use client';

import { ApiEndpoint } from '@/app/lib/api';
import { clientFetchWithAuth } from '@/app/lib/api.client';
import type { Order } from '@/app/types';
import { useJwtToken } from '@/app/utils/auth-client-helpers';
import { log } from '@repo/observability/log';
import { useState } from 'react';
import { mutate } from 'swr';
import type { OrderFormData } from '../schemas/order-form-schema';
import { removeEmptyObjects } from '../utils/form-utils';

/**
 * Custom hook for creating or updating an order
 * This uses direct client -> core API communication
 */
export function useCreateOrder() {
  const { jwt } = useJwtToken();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createOrder = async (orderData: OrderFormData): Promise<void> => {
    if (!jwt) {
      setError(new Error('Authentication required'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Clean up empty objects before sending to API
      const cleanedData = removeEmptyObjects(orderData);

      const order = await clientFetchWithAuth<{ data: Order }>(
        ApiEndpoint.ORDERS,
        jwt,
        {
          method: 'POST',
          body: JSON.stringify(cleanedData),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      log.info('Order created successfully', { order });

      mutate(
        (url) => typeof url === 'string' && url.startsWith(ApiEndpoint.ORDERS)
      );
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to create order');
      log.warn('Failed to create order in core backend', { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const createOrderWithSubmittedDocument = async (
    orderData: OrderFormData
  ): Promise<void> => {
    if (!jwt) {
      setError(new Error('Authentication required'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // transform orderData
      const transformedOrderData = {
        ...orderData,
        ...(orderData.isConsolidate
          ? {}
          : {
              buyer: {
                name: orderData.buyerName,
                tin: orderData.buyerTin,
                registrationType: orderData.registrationType,
                registrationNumber: orderData.registrationNumber,
                sstRegistrationNumber: orderData.sstRegistrationNumber,
                email: orderData.email,
                address: orderData.address,
                contactNumber: orderData.contactNumber,
              },
            }),
        invoiceDateTime: {
          date: orderData.invoiceDateTime,
          time: orderData.invoiceDateTime,
        },
      };

      // Clean up empty objects before sending to API
      const cleanedData = removeEmptyObjects(transformedOrderData);

      const order = await clientFetchWithAuth<{ data: Order }>(
        ApiEndpoint.ORDERS_WITH_DOCUMENT,
        jwt,
        {
          method: 'POST',
          body: JSON.stringify(cleanedData),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      log.info('Order created successfully', { order });

      mutate(
        (url) => typeof url === 'string' && url.startsWith(ApiEndpoint.ORDERS)
      );
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to create order');
      log.warn('Failed to create order in core backend', { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    createOrder,
    createOrderWithSubmittedDocument,
    isLoading,
    error,
  };
}
