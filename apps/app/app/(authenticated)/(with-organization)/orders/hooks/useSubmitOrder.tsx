"use client";

import { ApiEndpoint } from "@/app/lib/api";
import { clientFetchWithAuth } from "@/app/lib/api.client";
import { useJwtToken } from "@/app/utils/auth-client-helpers";
import { log } from "@repo/observability/log";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { mutate } from "swr";

/**
 * Custom hook for submit an order to MyInvois with the order id
 * This uses direct client -> core API communication
 */
export function useSubmitOrder() {
  const { jwt } = useJwtToken();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const submitOrderToMyInvois = async (id: number): Promise<void> => {
    if (!jwt) {
      setError(new Error("Authentication required"));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await clientFetchWithAuth(
        `${ApiEndpoint.ORDERS}/${id}/submit-to-myinvois`,
        jwt,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      log.info("Order submitted to MyInvois successfully", { id });

      // Navigate back to orders list
      router.push("/orders");

      mutate(
        (url) => typeof url === "string" && url.startsWith(ApiEndpoint.ORDERS)
      );
    } catch (err) {
      const error =
        err instanceof Error
          ? err
          : new Error("Failed to submit order to MyInvois");
      log.warn("Failed to submit order to MyInvois in core backend", { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    submitOrderToMyInvois,
    isLoading,
    error,
  };
}
