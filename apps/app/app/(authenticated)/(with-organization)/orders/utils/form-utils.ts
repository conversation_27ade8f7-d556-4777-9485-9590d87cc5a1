'use client';

/**
 * Checks if an object is empty (has no own properties)
 * @param obj The object to check
 * @returns True if the object is empty, false otherwise
 */
export function isEmptyObject(obj: Record<string, any>): boolean {
  if (!obj) {
    return true;
  }

  // Check if it's an object
  if (typeof obj !== 'object' || obj === null || Array.isArray(obj)) {
    return false;
  }

  // Check if it has any own properties
  return Object.keys(obj).length === 0;
}

/**
 * Checks if a nested object is effectively empty (all properties are null, undefined, empty strings, or empty objects)
 * @param obj The object to check
 * @returns True if the object is effectively empty, false otherwise
 */
export function isEffectivelyEmpty(obj: Record<string, any>): boolean {
  if (!obj) {
    return true;
  }

  // Check if it's an object
  if (typeof obj !== 'object' || obj === null || Array.isArray(obj)) {
    return false;
  }

  // Special case: Date objects are never empty
  if (obj instanceof Date) {
    return false;
  }

  // Special case: Objects with date or time properties are likely not empty
  if ('date' in obj || 'time' in obj) {
    // Check if the date/time values are valid
    if (
      (obj.date && obj.date instanceof Date) ||
      (obj.time && obj.time instanceof Date) ||
      (typeof obj.date === 'string' && obj.date) ||
      (typeof obj.time === 'string' && obj.time)
    ) {
      return false;
    }
  }

  // Check each property
  for (const key in obj) {
    const value = obj[key];

    // If the value is null, undefined, or an empty string, continue checking other properties
    if (value === null || value === undefined || value === '') {
      continue;
    }

    // If the value is an object, check if it's effectively empty
    if (typeof value === 'object' && !Array.isArray(value)) {
      // Special case: Date objects are never empty
      if (value instanceof Date) {
        return false;
      }

      if (!isEffectivelyEmpty(value)) {
        return false; // Found a non-empty nested object
      }
    } else if (Array.isArray(value)) {
      // If it's an array with items, it's not empty
      if (value.length > 0) {
        return false;
      }
    } else {
      // If it's any other non-empty value, the object is not effectively empty
      return false;
    }
  }

  // All properties are empty
  return true;
}

/**
 * List of special field names that should never be removed even if they appear empty
 */
const PRESERVE_FIELDS = [
  'invoiceDateTime',
  'date',
  'time',
  // 'deliveryDetails',
  // 'foreignCurrency',
  // 'billingPeriod',
];

/**
 * Recursively removes empty objects, null values, and undefined values from an object
 * @param obj The object to clean
 * @param parentKey The key of the parent object (used for preserving special fields)
 * @returns A new object with empty objects, null values, and undefined values removed
 */
export function removeEmptyObjects<T>(obj: T, parentKey?: string): T {
  // If it's not an object or it's null, return it as is
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  // Special case: preserve certain fields even if they appear empty
  if (parentKey && PRESERVE_FIELDS.includes(parentKey)) {
    return obj;
  }

  // If it's an array, process each item
  if (Array.isArray(obj)) {
    return obj
      .map((item) => removeEmptyObjects(item))
      .filter((item) => {
        if (typeof item === 'object' && item !== null && !Array.isArray(item)) {
          return !isEffectivelyEmpty(item as Record<string, any>);
        }
        return true;
      }) as unknown as T;
  }

  // Process object properties
  const result: Record<string, any> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];

      // Skip null or undefined values
      if (value === null || value === undefined) {
        continue;
      }

      // Special case: preserve certain fields even if they appear empty
      if (PRESERVE_FIELDS.includes(key)) {
        result[key] = value;
        continue;
      }

      // Process nested objects
      if (typeof value === 'object' && !Array.isArray(value)) {
        const cleanedValue = removeEmptyObjects(value, key);

        // Only add non-empty objects unless it's a special field
        if (
          !isEffectivelyEmpty(cleanedValue as Record<string, any>) ||
          PRESERVE_FIELDS.includes(key)
        ) {
          result[key] = cleanedValue;
        }
      } else if (Array.isArray(value)) {
        // Process arrays
        const cleanedArray = removeEmptyObjects(value);

        // Only add non-empty arrays
        if (cleanedArray.length > 0) {
          result[key] = cleanedArray;
        }
      } else {
        // Add primitive values
        result[key] = value;
      }
    }
  }

  return result as T;
}
