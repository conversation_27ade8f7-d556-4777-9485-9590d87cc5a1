'use server';

import { ApiEndpoint, fetchWithAuth } from '@/app/lib/api';
import type { SubmittedDocument } from '@/app/types';
import { getJwtFromSession } from '@/app/utils/auth-helpers';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { log } from '@repo/observability/log';

type CoreResponse<T> = {
  meta: {
    total: number;
    per_page?: number;
    current_page?: number;
    last_page?: number;
  };
  data: T[];
};

/**
 * Fetches submitted documents for a specific order
 * @param orderId The ID of the order to fetch documents for
 * @returns A promise that resolves to the submitted documents
 */
export async function getOrderDocuments(
  orderId: string
): Promise<CoreResponse<SubmittedDocument>> {
  try {
    const documents = await fetchWithAuth<CoreResponse<SubmittedDocument>>(
      urlSerialize(ApiEndpoint.SUBMITTED_DOCUMENTS, {
        order_id: orderId,
      }),
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getOrderDocuments', { documents, orderId });
    return documents;
  } catch (error) {
    log.warn('Failed to fetch order documents from core backend', {
      error,
      orderId,
    });
    throw new Error('Failed to fetch order documents from core backend');
  }
}
