'use client';

import { CreateOrganization } from '@repo/auth/components/create-organization';
import { useRouter } from 'next/navigation';

export function CreateOrganizationWrapper() {
  const router = useRouter();
  
  return (
    <CreateOrganization 
      onSuccess={() => {
        // Redirect to onboarding after successful organization creation
        router.push('/onboarding');
      }}
      onError={(error) => {
        // Error handling is done in the component
        console.error('Failed to create organization:', error);
      }}
    />
  );
}
