'use client';

import { useOrganization } from '@/lib/organization-context';
import { Building } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export function TopNavigation() {
  const { organization } = useOrganization();
  const pathname = usePathname();

  const navItems = [
    { name: 'Registration', href: '/' },
    { name: 'Document Tracking', href: '/tracking' },
  ];

  return (
    <header className="container relative z-10 mx-auto w-full border-b">
      <div className="flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            {organization?.logo ? (
              <Image
                src={organization.logo}
                alt={organization.name}
                width={32}
                height={32}
                className="rounded-lg"
              />
            ) : (
              <Building className="size-4" />
            )}
          </div>
          <span className="font-medium">
            {organization ? organization.name : 'Invoice Portal'}
          </span>
        </div>

        <nav className="flex items-center gap-6">
          {navItems.map((item) => {
            const isActive =
              item.href === '/'
                ? pathname === '/' || pathname.startsWith('/step')
                : pathname.startsWith(item.href);

            return (
              <Link
                key={item.href}
                href={item.href}
                className={`font-medium text-sm transition-colors hover:text-primary ${
                  isActive ? 'text-primary' : 'text-muted-foreground'
                }`}
              >
                {item.name}
              </Link>
            );
          })}
        </nav>
      </div>
    </header>
  );
}
