'use client';

import { FormLoading } from '@/components/ui/form-loading';
import { useFormStore } from '@/lib/form-store';
import { useHydratedForm } from '@/lib/hooks/use-hydrated-form';
import { step1Schema } from '@/lib/validations/registration';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';

import type { z } from 'zod';

export function IdInformationStep() {
  const { updateFormData, setCurrentStep, taxIdVerified, setTaxIdVerified } =
    useFormStore();

  // Use the custom hook for form hydration
  const { form, isHydrated } = useHydratedForm<z.infer<typeof step1Schema>>(
    step1Schema,
    (formData) => ({
      idType: formData.idType,
      identificationNumber: formData.identificationNumber,
      taxIdentificationNo: formData.taxIdentificationNo,
    }),
    {
      idType: 'BRN', // Default value before hydration
      identificationNumber: '',
      taxIdentificationNo: '',
    }
  );

  // Handle form submission
  const onSubmit = (data: z.infer<typeof step1Schema>) => {
    // Check if tax ID is verified
    if (!taxIdVerified) {
      form.setError('taxIdentificationNo', {
        type: 'manual',
        message:
          'Please verify your Tax Identification Number before proceeding',
      });
      return;
    }

    // Update the store with form data
    updateFormData(data);

    // Navigate to the next step
    setCurrentStep(1);
  };

  // TODO: Mock function to verify tax ID
  const verifyTaxId = () => {
    const taxId = form.getValues('taxIdentificationNo');
    if (!taxId) {
      form.setError('taxIdentificationNo', {
        type: 'manual',
        message: 'Please enter a Tax Identification Number',
      });
      return;
    }

    // TODO: In a real app, this would be an API call
    // Show loading state
    const verifyButton = document.getElementById('verify-tax-id');
    if (verifyButton) {
      verifyButton.textContent = 'Verifying...';
      verifyButton.setAttribute('disabled', 'true');
    }

    setTimeout(() => {
      setTaxIdVerified(true);
      toast.success('Tax ID verified successfully');

      // Update button state
      if (verifyButton) {
        verifyButton.textContent = 'Verified';
      }
    }, 1000);
  };

  // Show loading state if not hydrated yet
  if (!isHydrated) {
    return <FormLoading />;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="idType"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>ID Type</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value} // Use value instead of defaultValue
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select ID Type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="BRN">
                    Business Registration Number
                  </SelectItem>
                  <SelectItem value="NRIC">NRIC</SelectItem>
                  <SelectItem value="PASSPORT">Passport</SelectItem>
                  <SelectItem value="ARMY">ARMY</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="identificationNumber"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Identification Number</FormLabel>
              <FormControl>
                <Input placeholder="Enter identification number" {...field} />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="taxIdentificationNo"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Tax Identification Number</FormLabel>
              <div className="flex space-x-2">
                <FormControl>
                  <Input
                    placeholder="Enter tax identification number"
                    {...field}
                    className="flex-1"
                  />
                </FormControl>
                <Button
                  id="verify-tax-id"
                  type="button"
                  variant="outline"
                  onClick={verifyTaxId}
                  disabled={taxIdVerified}
                >
                  {taxIdVerified ? 'Verified' : 'Verify'}
                </Button>
              </div>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <div className="flex justify-end pt-4">
          <Button type="submit">Next</Button>
        </div>
      </form>
    </Form>
  );
}
