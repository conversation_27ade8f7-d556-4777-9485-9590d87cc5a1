'use client';

import { FormLoading } from '@/components/ui/form-loading';
import { useFormStore } from '@/lib/form-store';
import { useHydratedForm } from '@/lib/hooks/use-hydrated-form';
import { msicOptions } from '@/lib/msic';
import { step3Schema } from '@/lib/validations/registration';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@repo/design-system/components/ui/command';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import LocationSelector from '@repo/design-system/components/ui/location-input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import { ScrollArea } from '@repo/design-system/components/ui/scroll-area';
import countries from '@repo/design-system/data/countries.json';
import { cn } from '@repo/design-system/lib/utils';
import { Check, ChevronsUpDown } from 'lucide-react';
import { useState } from 'react';
import type { z } from 'zod';

export function BusinessDetailsStep() {
  const { updateFormData, setCurrentStep } = useFormStore();
  const [open, setOpen] = useState(false);

  // Use the custom hook for form hydration
  const { form, isHydrated } = useHydratedForm<z.infer<typeof step3Schema>>(
    step3Schema,
    (formData) => ({
      businessRegistrationNoOld: formData.businessRegistrationNoOld,
      msicCode: formData.msicCode,
      sstNo: formData.sstNo,
      addressLine1: formData.addressLine1,
      addressLine2: formData.addressLine2,
      addressLine3: formData.addressLine3,
      postcode: formData.postcode,
      city: formData.city,
      state: formData.state,
      country: formData.country,
      invoiceNo: formData.invoiceNo,
      invoiceAmount: formData.invoiceAmount,
    }),
    {
      businessRegistrationNoOld: '',
      msicCode: '',
      sstNo: '',
      addressLine1: '',
      addressLine2: '',
      addressLine3: '',
      postcode: '',
      city: '',
      state: '',
      country: '',
      invoiceNo: '',
      invoiceAmount: '',
    }
  );

  // Handle form submission
  const onSubmit = (data: z.infer<typeof step3Schema>) => {
    // Update the store with form data
    updateFormData(data);

    // Navigate to the next step
    setCurrentStep(3);
  };

  // Handle back button click
  const handleBack = () => {
    // Save current form values to store even if incomplete
    updateFormData(form.getValues());

    // Navigate to the previous step
    setCurrentStep(1);
  };

  // Show loading state if not hydrated yet
  if (!isHydrated) {
    return <FormLoading />;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="businessRegistrationNoOld"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Business Registration No. (Old)</FormLabel>
                <FormControl>
                  <Input placeholder="Optional" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
          <FormField
            control={form.control}
            name="msicCode"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>MSIC Code</FormLabel>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        // biome-ignore lint/a11y/useSemanticElements: <explanation>
                        role="combobox"
                        aria-expanded={open}
                        className="w-full justify-between overflow-hidden"
                      >
                        <span className="truncate">
                          {field.value
                            ? msicOptions.find(
                                (option) => option.Code === field.value
                              )?.Description
                            : 'Select MSIC Code...'}
                        </span>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-[400px] p-0">
                    <Command>
                      <CommandInput placeholder="Search MSIC code or description..." />
                      <CommandEmpty>No MSIC code found.</CommandEmpty>
                      <CommandGroup className="max-h-[300px] overflow-y-auto">
                        <ScrollArea className="h-72">
                          {msicOptions.map((option) => (
                            <CommandItem
                              key={option.Code}
                              value={`${option.Code} ${option.Description}`}
                              onSelect={() => {
                                field.onChange(option.Code);
                                setOpen(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  field.value === option.Code
                                    ? 'opacity-100'
                                    : 'opacity-0'
                                )}
                              />
                              {option.Code} - {option.Description}
                            </CommandItem>
                          ))}
                        </ScrollArea>
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="sstNo"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>SST No.</FormLabel>
              <FormControl>
                <Input placeholder="Optional" {...field} />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="addressLine1"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Address Line 1</FormLabel>
              <FormControl>
                <Input placeholder="Enter address line 1" {...field} />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="postcode"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Postcode</FormLabel>
                <FormControl>
                  <Input placeholder="Enter postcode" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
          <FormField
            control={form.control}
            name="city"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>City</FormLabel>
                    <FormControl>
                  <Input placeholder="Enter city" {...field} />
                    </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <GridFormItem>
            <FormLabel>Location</FormLabel>
            <FormControl>
              <LocationSelector
                defaultCountry={countries.find(
                  (country) => country.iso2 === 'MY'
                )}
                onCountryChange={(country) => {
                  if (country) {
                    form.setValue('country', country.name);
                  } else {
                    form.setValue('country', '');
                  }
                }}
                onStateChange={(state) => {
                  if (state) {
                    form.setValue('state', state.state_code);
                  } else {
                    form.setValue('state', '');
                  }
                }}
              />
            </FormControl>
            <FormMessage />
          </GridFormItem>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="invoiceNo"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Invoice No.</FormLabel>
                <FormControl>
                  <Input placeholder="Enter invoice number" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
          <FormField
            control={form.control}
            name="invoiceAmount"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Invoice Amount</FormLabel>
                <FormControl>
                  <Input placeholder="Enter invoice amount" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="flex justify-between pt-4">
          <Button type="button" variant="outline" onClick={handleBack}>
            Back
          </Button>
          <Button type="submit">Next</Button>
        </div>
      </form>
    </Form>
  );
}
