'use client';

import { log } from '@repo/observability/log';
import type * as React from 'react';
import { createContext, useContext, useEffect, useState } from 'react';

// Define regex patterns at the top level for better performance
const ORG_ID_REGEX = /x-organization-id=([^;]+)/;
const ORG_NAME_REGEX = /x-organization-name=([^;]+)/;
const ORG_SLUG_REGEX = /x-organization-slug=([^;]+)/;
const ORG_LOGO_REGEX = /x-organization-logo=([^;]+)/;

export type Organization = {
  id: string;
  name: string;
  slug: string;
  logo?: string | null;
};

export type OrganizationContextType = {
  organization: Organization | null;
  isOrganizationPortal: boolean;
};

export const OrganizationContext = createContext<OrganizationContextType>({
  organization: null,
  isOrganizationPortal: false,
});

export const useOrganization = () => useContext(OrganizationContext);

export const getOrganizationFromHeaders = (): Organization | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  // Get organization information from cookies or localStorage
  const organizationId =
    document.cookie.match(ORG_ID_REGEX)?.[1] ||
    localStorage.getItem('x-organization-id');
  const organizationName =
    document.cookie.match(ORG_NAME_REGEX)?.[1] ||
    localStorage.getItem('x-organization-name');
  const organizationSlug =
    document.cookie.match(ORG_SLUG_REGEX)?.[1] ||
    localStorage.getItem('x-organization-slug');
  const organizationLogo =
    document.cookie.match(ORG_LOGO_REGEX)?.[1] ||
    localStorage.getItem('x-organization-logo');

  if (!organizationId || !organizationName) {
    return null;
  }

  return {
    id: organizationId,
    name: decodeURIComponent(organizationName),
    slug: organizationSlug ? decodeURIComponent(organizationSlug) : '',
    logo: organizationLogo ? decodeURIComponent(organizationLogo) : null,
  };
};

export const OrganizationProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [organization, setOrganization] = useState<Organization | null>(null);

  useEffect(() => {
    // Try to get organization from headers
    const org = getOrganizationFromHeaders();

    if (org) {
      setOrganization(org);
    } else {
      // If not found in headers, try to get from request headers
      const fetchOrganizationFromHeaders = async () => {
        try {
          const response = await fetch('/api/organization');
          if (response.ok) {
            const data = await response.json();
            if (data.organization) {
              setOrganization(data.organization);
            }
          }
        } catch (error) {
          log.warn('Error fetching organization:', { error });
        }
      };

      fetchOrganizationFromHeaders();
    }
  }, []);

  return (
    <OrganizationContext.Provider
      value={{
        organization,
        isOrganizationPortal: !!organization,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
};
