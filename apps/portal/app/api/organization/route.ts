import { headers } from 'next/headers';
import { NextResponse } from 'next/server';

export async function GET() {
  const headersList = await headers();

  const organizationId = headersList.get('x-organization-id');
  const organizationName = headersList.get('x-organization-name');
  const organizationSlug = headersList.get('x-organization-slug');
  const organizationLogo = headersList.get('x-organization-logo');

  if (!organizationId || !organizationName) {
    return NextResponse.json({ organization: null });
  }

  return NextResponse.json({
    organization: {
      id: organizationId,
      name: organizationName,
      slug: organizationSlug || '',
      logo: organizationLogo || null,
    },
  });
}
