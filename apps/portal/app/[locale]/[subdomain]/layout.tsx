import { TitleUpdater } from '@/components/title-updater';
import { env } from '@/env';
import { OrganizationProvider } from '@/lib/organization-context';
import type { Metadata } from 'next';
import type { ReactNode } from 'react';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ subdomain: string }>;
}): Promise<Metadata> {
  const appUrl = new URL(env.NEXT_PUBLIC_WEB_URL);
  const rootDomain = appUrl.host;

  const { subdomain } = await params;
  // TODO: probably find from redis?
  //   const subdomainData = await getSubdomainData(subdomain);

  //   if (!subdomainData) {
  //     return {
  //       title: rootDomain,
  //     };
  //   }

  return {
    title: `${subdomain}.${rootDomain}`,
    description: `Subdomain page for ${subdomain}.${rootDomain}`,
  };
}

export default async function SubdomainLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: Promise<{ subdomain: string }>;
}) {
  const { subdomain } = await params;
  // const subdomainData = await getSubdomainData(subdomain);

  // if (!subdomainData) {
  //   notFound();
  // }

  return (
    <OrganizationProvider>
      <TitleUpdater />
      {children}
    </OrganizationProvider>
  );
}
