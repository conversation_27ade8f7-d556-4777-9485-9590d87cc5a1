'use client';

import { useOrganization } from '@/lib/organization-context';
import { useSession } from '@repo/auth/client';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import { Building2, Globe, User } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export default function OrganizationPortalPage() {
  const { organization, isOrganizationPortal, isLoading } = useOrganization();
  const { data: session } = useSession();

  if (isLoading) {
    return (
      <div className="container mx-auto space-y-6 p-6">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isOrganizationPortal || !organization) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Organization Not Found
            </CardTitle>
            <CardDescription>
              The organization you're looking for doesn't exist or is not
              available.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/">Go to Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto space-y-6 p-6">
      <div className="space-y-2">
        <h1 className="font-bold text-3xl tracking-tight">
          Welcome to {organization.name}
        </h1>
        <p className="text-muted-foreground">
          You're viewing the portal for {organization.name}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Organization Details
            </CardTitle>
            <CardDescription>
              Information about this organization
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <strong>Name:</strong> {organization.name}
              </div>
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                <strong>Slug:</strong> {organization.slug}
              </div>
              <div className="flex items-center gap-2">
                <strong>ID:</strong>
                <code className="rounded bg-muted px-2 py-1 text-sm">
                  {organization.id}
                </code>
              </div>
              {organization.logo && (
                <div className="flex items-center gap-2">
                  <strong>Logo:</strong>
                  <Image
                    src={organization.logo}
                    alt={`${organization.name} logo`}
                    className="h-8 w-8 rounded"
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Session Information
            </CardTitle>
            <CardDescription>Your current session details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {session?.user ? (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <strong>User:</strong>{' '}
                  {session.user.name || session.user.email}
                </div>
                <div className="flex items-center gap-2">
                  <strong>Email:</strong> {session.user.email}
                </div>
                <div className="flex items-center gap-2">
                  <strong>User ID:</strong>
                  <code className="rounded bg-muted px-2 py-1 text-sm">
                    {session.user.id}
                  </code>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  You are not currently signed in.
                </p>
                <div className="flex gap-2">
                  <Button asChild>
                    <Link href="/sign-in">Sign In</Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/sign-up">Sign Up</Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Available Actions</CardTitle>
          <CardDescription>
            What you can do in this organization portal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button asChild>
              <Link href={`/${organization.slug}/registration`}>
                Registration
              </Link>
            </Button>
            {session?.user && (
              <Button variant="outline" asChild>
                <Link href="/dashboard">Go to Dashboard</Link>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
