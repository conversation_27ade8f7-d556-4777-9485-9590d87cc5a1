'use client';
import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { useState } from 'react';
import { z } from 'zod';

// Define the schema for document tracking
const documentTrackingSchema = z.object({
  documentId: z.string().min(1, { message: 'Document ID is required' }),
});

// Define the schema for invoice tracking
const invoiceTrackingSchema = z.object({
  invoiceId: z.string().min(1, { message: 'Invoice ID is required' }),
});

export default function TrackingPage() {
  const [searchResult, setSearchResult] = useState<null | {
    id: string;
    status: string;
    date: string;
    type: string;
  }>(null);

  // Document tracking form
  const documentForm = useForm<z.infer<typeof documentTrackingSchema>>({
    resolver: zodResolver(documentTrackingSchema),
    defaultValues: {
      documentId: '',
    },
  });

  // Invoice tracking form
  const invoiceForm = useForm<z.infer<typeof invoiceTrackingSchema>>({
    resolver: zodResolver(invoiceTrackingSchema),
    defaultValues: {
      invoiceId: '',
    },
  });

  // Handle document tracking form submission
  function onDocumentSubmit(values: z.infer<typeof documentTrackingSchema>) {
    // Simulate API call to track document
    setTimeout(() => {
      // Mock result
      setSearchResult({
        id: values.documentId,
        status: 'Processed',
        date: '2023-06-15',
        type: 'PDF',
      });
      toast.success(`Document ${values.documentId} found`);
    }, 500);
  }

  // Handle invoice tracking form submission
  function onInvoiceSubmit(values: z.infer<typeof invoiceTrackingSchema>) {
    // Simulate API call to track invoice
    setTimeout(() => {
      // Mock result
      setSearchResult({
        id: values.invoiceId,
        status: 'Paid',
        date: '2023-05-20',
        type: 'Invoice',
      });
      toast.success(`Invoice ${values.invoiceId} found`);
    }, 500);
  }

  return (
    <div className="container mx-auto p-6">
      <Card className="mx-auto max-w-2xl">
        <CardHeader>
          <CardTitle>Track Your Documents</CardTitle>
          <CardDescription>
            Enter your document or invoice ID to track its status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="document" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="document">Document</TabsTrigger>
              <TabsTrigger value="invoice">Invoice</TabsTrigger>
            </TabsList>
            <TabsContent value="document" className="mt-4">
              <Form {...documentForm}>
                <form
                  onSubmit={documentForm.handleSubmit(onDocumentSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={documentForm.control}
                    name="documentId"
                    render={({ field }) => (
                      <GridFormItem>
                        <FormLabel>Document ID</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter document ID" {...field} />
                        </FormControl>
                        <FormMessage />
                      </GridFormItem>
                    )}
                  />
                  <Button type="submit" className="w-full">
                    Track Document
                  </Button>
                </form>
              </Form>
            </TabsContent>
            <TabsContent value="invoice" className="mt-4">
              <Form {...invoiceForm}>
                <form
                  onSubmit={invoiceForm.handleSubmit(onInvoiceSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={invoiceForm.control}
                    name="invoiceId"
                    render={({ field }) => (
                      <GridFormItem>
                        <FormLabel>Invoice ID</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter invoice ID" {...field} />
                        </FormControl>
                        <FormMessage />
                      </GridFormItem>
                    )}
                  />
                  <Button type="submit" className="w-full">
                    Track Invoice
                  </Button>
                </form>
              </Form>
            </TabsContent>
          </Tabs>

          {searchResult && (
            <div className="mt-6 rounded-md border p-4">
              <h3 className="mb-2 font-medium">Search Result</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>ID:</div>
                <div>{searchResult.id}</div>
                <div>Status:</div>
                <div>{searchResult.status}</div>
                <div>Date:</div>
                <div>{searchResult.date}</div>
                <div>Type:</div>
                <div>{searchResult.type}</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
