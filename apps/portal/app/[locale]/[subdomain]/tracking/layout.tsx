import { TitleUpdater } from '@/components/title-updater';
import { TopNavigation } from '@/components/top-navigation';
import { OrganizationProvider } from '@/lib/organization-context';
import { Toaster } from '@repo/design-system/components/ui/sonner';
import type * as React from 'react';

type TrackingLayoutProps = {
  children: React.ReactNode;
};

export default function TrackingLayout({ children }: TrackingLayoutProps) {
  return (
    <OrganizationProvider>
      <TitleUpdater />
      <div className="flex min-h-screen flex-col">
        <TopNavigation />
        <div className="flex-1">{children}</div>
      </div>
      <Toaster />
    </OrganizationProvider>
  );
}
