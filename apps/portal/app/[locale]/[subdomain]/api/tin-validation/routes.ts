import { env } from '@/env';
import { type NextRequest, NextResponse } from 'next/server';

const postHandler = async (request: NextRequest) => {
  const body = await request.json();

  const response = await fetch('/api/v1/tin-validation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Invois-Key': env.CORE_OUTGOING_API_KEY,
      'X-Organization-ID': '1', // get from subdomain
    },
    body,
  });

  return NextResponse.json(response, {
    status: 200,
  });
};

// export const OPTIONS = postHandler;
export const GET = postHandler;
